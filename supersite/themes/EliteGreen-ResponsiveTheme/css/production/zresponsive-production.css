/* Mobile & Tablet View Common */

@media only screen and (max-width: 768px), (min-width: 769px) and (max-width: 1000px)
{
    .contact-support-banner,
    .kb-support,
    .complaint-support,
    .contact-support-banner-content
    {
        width: 100%;
        margin: 0;
        padding: 0;
    }

    #page-container.contact-support-page
    { padding: 0; }

    .contact-support-container h1 span.prev, .complaint-support h2 span.prev,
    .contact-support-container h1 span.next, .complaint-support h2 span.next
    { display: none; }

}

/* Mobile View (above 320px and below 768px) */

@media only screen and (min-width: 320px) and (max-width: 767px)
{
    .contact-support-form, .complaint-support .com-links-wrapper
    {  width: 75.5%;  padding: 0 6%; box-shadow: none; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; }


    /* Banner */

    .contact-support-banner
    { background: url('/getImage.php?src=contact-support-banner-mobile.png') no-repeat center;
        border-top:1px solid #e6e6e6; box-shadow: inset 0 7px 9px -7px rgba(187, 187, 187, 0.8);
        background-size: cover;
    }

    .contact-support-banner-content { width: 87.5%; margin: 0 auto; text-align: center; }

    .contact-support-banner-content h1 { font: normal 20px 'Open Sans', sans-serif; padding: 25px 0 10px 0; margin: 0; }

    .contact-support-banner-content p
    {  font: 400 14px/20px 'Open Sans', sans-serif; text-align: center; padding:0 0 185px 0; margin: 0; }

    .contact-support-banner-content p br { display: none; }


    /* Contact form & Support Information */

    .contact-support-container .lfloat,
    .contact-support-container .rfloat,
    .complaint-support .com-links-wrapper .complaints:first-child,
    .complaint-support .com-links-wrapper .complaints
    {  display: block;  float: none;  margin: 0;  padding: 0;  width: 100%;  }

    .complaint-support .com-links-wrapper .complaints:first-child,
    .complaint-support .com-links-wrapper .complaints
    { padding: 16px 0 0 0; }

    .complaint-support .com-links-wrapper .complaints:first-child
    { border: 0; border-bottom: 1px solid #ddd; }

    .contact-support-container
    { width: 100%; padding: 0; margin: 0; }

    .contact-support-form h3
    { font: normal 16px 'Open Sans', sans-serif; margin: 16px 0 8px 0; }

    .contact-support-container .contact-form-field
    { display: block; margin-bottom: -10px; }

    .contact-support-container .lfloat label.frm-label
    { font-size:14px }

    .contact-support-container #contact-form select
    { font: 400 14px 'Open Sans', sans-serif }

    .contact-support-container #contact-form input[type="text"],
    .contact-support-container #contact-form textarea
    { padding: 11px 0 11px 3%; width: 96%; }

    .contact-support-container #contact-form input[type="submit"]
    { font-size: 15px; padding: 10px 0; width: 100%; }

    .contact-support-container .rfloat .support-info td
    { padding: 11px 6.2% 7px 6.2%; width: 87.5%; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; }

    .contact-support-container .rfloat .support-info td p
    { font-size: 14px; margin: 0; padding: 0 0 13px 0; }

    .contact-support-container .rfloat h5.ui-heading
    { font: 600 14px/27px 'Open Sans', sans-serif; padding: 0 0 6px 0;  margin: -5px 0 17px 0; }

    .contact-support-container #contact-form select { width: 100%; }

    .contact-support-container .rfloat .support-info tr.space td
    { height: 20px; }

    .contact-support-container .rfloat .support-info { margin: 0; }

    .contact-support-container .rfloat .support-info tr:last-child td {
        margin: 0 0 20px 0;
    }

    .contact-support-container .rfloat h3
    {
        font: normal 16px 'Open Sans', sans-serif;
        margin: 25px 0 15px 0;
        border-top: 1px solid #ddd;
        padding: 15px 0 0 0; position: relative;
    }
    .support-contacts-toggle
    { display: block; position: absolute; right:0; top:18px; background:url('/getImage.php?src=supportcontacts-toggle.png');
      height: 9px; width: 16px;
    }
    .support-contacts-toggle.downarrow
    {
        -webkit-transform: rotate(180deg);
        -moz-transform: rotate(180deg);
        -ms-transform: rotate(180deg);
        -o-transform: rotate(180deg);
        transform: rotate(180deg);
    }

    /* KB */

    .kb-support  p { font-size: 14px; margin: 0 auto 14px auto; width: 87.5%; }

    .kb-support { padding: 20px 0 30px 0; }

    .kb-support a { font-size: 15px; display: block; width: 74%; margin: 0 auto;  }

    .kb-support a:hover { color:#fff !important; }


    /* Complaint */

    .complaint-support .com-links-wrapper .complaints h3
    { font-size: 14px; margin: 0 0 10px 0; }

    .complaint-support .com-links-wrapper .complaints p {  margin: 0 0 18px 0;  }

    .complaint-support h2,
    .kb-support h2
    { font: normal 20px 'Open Sans', sans-serif; }

    .complaint-support h2
    { text-align: left; width: 87.5%; margin: 45px auto 15px auto; padding: 0; }

    .complaint-support .com-links-wrapper .complaints p a
    { margin: 3px 0 0 0; }



}

/* Tablet View (above 768px and below 1000px) */

@media screen and (min-width: 768px) and (max-width: 1000px)
{

    /* Banner */

    .contact-support-banner
    { background: url('/getImage.php?src=contact-support-banner-tab.png') no-repeat center;
      border-top:1px solid #e6e6e6; box-shadow: inset 0 7px 9px -7px rgba(187, 187, 187, 0.8);
      background-size: cover;
    }

    .contact-support-banner-content h1 {  font: 600 26px 'Open Sans', sans-serif; padding: 35px 0 10px 20px;  }

    .contact-support-banner-content p
    { font: 400 15px 'Open Sans', sans-serif; padding: 0 0 90px 20px; }


    /* Contact form & Support Information */

    .complaint-support .com-links-wrapper, .contact-support-container
    { width: 95%; }

    .contact-support-container .rfloat .support-info { width: 100%; }

    .contact-support-container .lfloat,
    .contact-support-container .rfloat {
        display: block;
        float: none;
        width: 100%;
    }

    .contact-support-container .rfloat .support-info tr
    { display: inline-block; vertical-align: top; width: 39%; }

    .contact-support-container .rfloat .support-info tr.space,
    .contact-support-container .rfloat .support-info tr.space td
    { display: none; }

    .contact-support-container .rfloat .support-info tr:nth-child(1),
    .contact-support-container .rfloat .support-info tr:nth-child(3)
    { margin-bottom: 20px; }

    .contact-support-container .rfloat .support-info tr:nth-child(1),
    .contact-support-container .rfloat .support-info tr:nth-child(5)
    { margin-right: 16%;  }

    .contact-support-container .rfloat .support-info td
    { display: inline-block; vertical-align: top; width: 100%; }

    .contact-form-field { display: inline-block; width: 45%; }

    .contact-form-field:nth-child(1),
    .contact-form-field:nth-child(3)
    { margin-right: 9%; }

    .contact-support-container .lfloat label.frm-label { font-size: 14px; margin-bottom: -1px; }

    .contact-support-container #contact-form input[type="text"].frm-field
    { width: 97%; padding-left: 3%;  }

    .contact-support-container #contact-form select
    { background: url(/getImage.php?src=images/selectbox-icon.png) no-repeat right 3px top 3px #fff;
      font: 400 14px 'Open Sans', sans-serif; width: 100%;
    }

    .contact-support-container #contact-form textarea
    {  padding: 5px 0 5px 3%;  width: 97%;  }

    .contact-support-container .contact-support-form
    { padding: 25px 30px 30px 30px; box-shadow: none; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; }

    .contact-support-form .rfloat h3 { margin: 45px 0 0 0; }


    /* KB */

    .kb-support { padding: 45px 0 50px 0; margin: 50px 0 0 0; }

    .kb-support h2 { font: normal 22px 'Open Sans', sans-serif; }

    .kb-support a { font-size: 15px; padding: 12px 30px; display: block; max-width: 220px; margin: 0 auto; }

    .kb-support p { margin: 0 0 20px }


    /* Complaint */

    .complaint-support h2
    { font: normal 22px 'Open Sans', sans-serif;  margin: 35px auto 0 auto; }

    .complaint-support .com-links-wrapper .complaints
    {  width: 45%; padding: 0 0 0 3.8%;  }

    .complaint-support .com-links-wrapper .complaints:first-child
    { padding: 0 0 0 3.8%;  width: 44%; }

}.mobile-menu { display: none; }

/* Mobile & Tablet View Common */

@media only screen and (max-width: 768px), (min-width: 769px) and (max-width: 1000px)
{

    /* Common */

    body { min-width: 320px; }

    #page-container { width: 100%; }

    .footer-cols,
    #top-header,
    #primary-nav
    { display: none; }

    input[type="button"],input[type="submit"]
    { -webkit-appearance: none; -moz-appearance: none; }

    input[type="text"],input[type="password"]
    { font-family: 'Open Sans', sans-serif !important; font-size: 14px !important; font-weight: 400 !important;
      -webkit-appearance: none !important; -moz-appearance: none !important;
    }

    textarea:focus, input:focus{
        outline: 0;
    }

    *:focus {
        outline: 0;
    }

    /* Header */

    .home-bg #page-header { margin: 0; padding: 0; }

    #page-header .new-header-style { padding: 0 6%; }

    #page-header .new-header-style h1#branding .logo-cont { padding: 0; }

    h1#branding { float: left; margin: 0 !important; width: 50% !important; height: auto; }

    #page-header h1 { font-size: inherit; }

    #page-header h1#branding .logo-cont { line-height: normal; height: auto; }

    #branding .logo-cont img{ max-height: 100%; max-width: 94%; margin: 25px 0 23px 0; }

    .right-header { margin: -11.5px 0 0 0; text-align:right; width: 30%; position: absolute; right: 2.6%; top: 50%;  }

    .right-header a, .right-header .responsive-menu-icon
    { display: inline-block; *display: inline; *position: relative; zoom:1; }

    .right-header a
    { background: url("/getImage.php?src=images/responsive-cart-img.png") no-repeat; cursor: pointer;  height: 20px;
        margin: 0 17px 0 0; width: 21px; }

    .right-header .responsive-menu-icon
    { background: url("/getImage.php?src=images/responsive-menu.png") no-repeat; cursor: pointer; height: 14px;
        margin: 4px 0 0 0; vertical-align: top; width: 23px;
    }

    #page-container.upper-shadow
    { border-top:1px solid #e6e6e6; box-shadow: inset 0 7px 9px -7px rgba(187, 187, 187, 0.6); }


    /* Footer */

    #page-footer { min-width: 0; }

    #footer-wrapper { width: 100%; padding: 0; }

    .new-footer-style, .copyright-info-wrapper, .copyright-info-container { margin:0 auto; text-align: center; width: 100% !important; }

    .new-footer-style #fineprint, #page-footer .new-footer-style a, .copyright-info-container span a { font-size: 12px; }

    #page-footer .new-footer-style .copyright-info-wrapper { position: static; }

    .new-footer-style #fineprint { text-align:center; width: 100%; }

    .footerSpan { margin:0; display: block; text-align: center; width: 100%; }

    .footerLinks { background: #222; display: block; margin: -15px 0 0 0;  padding: 20px 0; width: 100%;  }

    .footerLinks li { display: inline-block; }

    .copyright-info-container span.rfloat { display: none; }

    /* Main Menu */

    .noscartitems { background: #ffc600;
        font: bold 11px 'Open Sans', sans-serif; font-weight: 400; color: #333;
        border-radius: 10px;
        display: inline-block;
        position: relative;
        top: -13px; right:-28px;
        width: 13px;
        height: 13px; line-height: 13.5px;
        text-align: center;
        z-index: 99;
    }

    .new-header-style { position: relative; }

    .mobile-menu
    { background: #fff; box-shadow: 1px 1px 5px #bbb; -webkit-appearance: none;
      padding-top:50px;  position: absolute; right: 0; top: 0; z-index: 99999; display: none; width: 100%;
    }

    .mobile-menu .menu-content p, .mobile-menu li.levelone ul li h3,
    .mobile-menu li.levelone ul,
    .mobile-menu li.levelone ul.submenu-parent li ul,
    .mobile-menu li.levelone ul.submenu-parent li:first-child ul,
    .mobile-menu li .mnew-tag
    { display: none; }

    .mobile-menu li { font-family: 'Open Sans', sans-serif; }

    .mobile-menu li.levelone, .mobile-menu li.first
    {  font-family:'Open Sans', sans-serif; font-size:14px; font-weight: 400;
       border-bottom:1px solid #ddd;  text-transform: uppercase; display: block; margin: 0 20px 0 5px; padding: 15px 0 5px 0 ;
       cursor: pointer;
    }

    .mobile-menu li.levelone a, .mobile-menu li.first a
    { color: #333; display: block; }

    .mobile-menu li.first
    { padding: 0px 0 15px 0; }

    .mobile-menu li.link-to-desktop
    { color:#377ce4; font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400;
      display: block; margin: 15px 20px 0 20px; text-transform: uppercase;
    }

    .mobile-menu li.login-signup
    { color:#377ce4; font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400;
        display: block; margin: 38px 20px 40px 20px;  border: 1px solid #377ce4; text-align: center;
        border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px; text-transform: uppercase;
    }

    .mobile-menu li.login-signup a {  display: block;  padding: 12px 0;  }

    .mobile-menu li.login-signup a:hover { color: #377ce4; }

    #header-wrapper .close-mobile-menu
    {  background: url("/getImage.php?src=/close-mobile-menu.png") no-repeat; height: 14px; width: 23px;
       cursor: pointer; position: absolute; top: 20px; right: 20px; z-index: 999999; display: none;
    }

    /* Homepage Pricing */

    .homepage-product-details span.price-symbol { margin: 10px 0 0 0 !important; }

    .homepage-product-details span.price-per { color:#000; font-size: 12px; text-align: right !important; }

    /* Domain Upsell error msg */

    #select-domain-content .inner-content .error-message
    { background: none;  border: 0;  margin: 10px 0 0 0; padding: 0; text-align: center; }

    #select-domain-content .inner-content .error-message li
    { color:#d84a49; font-size: 12px; font-weight: 400;  list-style: none; margin: 0; padding: 0; text-align: left; white-space: nowrap; }

    /* Checkout page */

    .CartSection { width: 100%; }

    /* forgot password */

    div.ui-heading
    { font-family: 'Open Sans', sans-serif;  font-size: 20px;  color: #1b1b1b; padding: 0;font-weight: bold;  }



}



/* Mobile View (above 320px and below 768px) */

@media only screen and (min-width: 320px) and (max-width: 767px)
{
    /* Header */

    #page-header h1#branding .logo-cont a { display: block; width: 150px; }

    /* Banner */

    #branding .logo-cont img { margin: 20px 0 18px 0; }

    .right-header { right: 6%; }

    /* Checkout */

    .cart-product-icon, .sub-products .cart-product-icon { margin: 0 6px 0 0 }

    #CartTable tr.even th { display: none; }

    .CartSection.row-indent { padding: 0; width: 100%; }

    #CartTable { width: 87.5%;  margin: -25px auto 0 auto; }

    .CartSection h2.ui-heading { width:87.5%; margin:0 auto;     }

    #CartTable td  { display: inline-block; }

    .cartItem-product-detail {width: 70% }

    td.CartItem, .domain_purchased { width: 100%; }

    #CartTable tr.CartItemRow { border-bottom: 1px solid #ccc; }

    #CartTable tr.CartItemRow td.CartItem { padding: 20px 0 9px 0; position: relative; border-bottom: 1px solid #eee; }

    .CartSection .intro-text.gray-box
    { border-bottom: 2px solid #b6b6b6; border-radius: 0; width: 87.5%; margin: 0 auto; }

    .CartSection h2.ui-heading
    { font-family: 'Open Sans', sans-serif; font-size: 20px; font-weight: bold;  padding: 5px 0 11px 0;  }

    .CartSection .intro-text.gray-box h2.ui-heading { width: 100%; }

    .CartSection .ItemTitle, .cartItem-product-detail .domain-name
    { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 600; margin: 0 0 0 0; width: 95%;
      overflow: hidden;  white-space: nowrap;  text-overflow: ellipsis;
    }

    .cart-userinfo-bar
    { display: block;  width: 100%;  float: none; margin-left: 0; }

    .cart-product-name { font-size: 7px; }

    tr.CartItemRow { position: relative; display: block; }

    .remove_item_class { position: absolute; right: 0; top: 20px; }

    .pp_total { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; }

    .pp_in_cart { bottom: 0; width: 72%; }

    /*.pp_in_cart .rfloat { float: none; }*/

    .pp_in_cart .remove_pp { background-size: 12px 12px; height: 12px; width: 12px; }

    .SetRowMinHeight
    { min-height: 0; padding: 0; }

    #CartTable td.CartDuration, #CartTable td.CartSubTotal, #CartTable td.item-close
    { padding: 11px 0; }

    #CartTable tr.CartItemRow td.CartDuration, #CartTable tr.CartItemRow td.CartSubTotal
    { border-bottom: 0;  }

    #CartTable tr.CartItemRow td.CartDuration { margin: 0 0 0 72px; }

    .CartSubTotal p.ItemSubTotal,
    #CartTable tr.CartItemRow td.CartDuration .dropdown-value,
    .CartDuration .price_dropdown li
    { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; color: #1b1b1b; }

    .CartSubTotal p { line-height: 18px; }

    #CartTable tr.CartItemRow td.CartSubTotal { float: right; }

    #CartTable tr.CartItemRow td.CartDuration .dropdown-value,
    .CartDuration .price_dropdown li
    { font-weight: 400; }

    .ItemTitle img { vertical-align: middle; width: 14px;  height: 11px; }

    #CartTable tr.CartItemRow td.CartDuration {  text-align: left;   }

    #CartTable .CartDuration .SavingsShow { padding: 0; text-align: left !important; }

    .CartDuration .dropdown-value
    { width: auto;  display: inline-block;  padding-right: 16px; }

    .pay-sep-or span { display: none; }

    .pay-sepr { background: none; }


    .ItemSubTitle br {
    content: "";  margin: 2em;  display: block;  font-size: 24%;
    }

    .CartDuration .price_dropdown, .shop-cart-table .update_item_class
    { left: 0; width: 195px; z-index: 9999; }

    .CartDuration .price_dropdown .up-arr { left: 37px; }

    .hideborderpartly { position: absolute;  display: block;  width: 72px;  height: 1px;  background: #fff;  bottom: -1px; }

    .invoiced { background: none !important; font-size: 11px !important; padding: 0 !important; }

    .pp_total { margin: 0 2px 0 0; }

    .pp_in_cart, .pp_total  { font-size: 12px; }

    /* Checkout page - Coupon Code */

    #CartTableFooter
    { width: 87.5%; margin: 0 auto -6px auto; position: relative; top:-1px; }

    #CartTableFooter td
    { display: block; margin: 15px;  }

    #couponCodeContainer
    { margin: -3px 0 0 0; }

    #couponCodeContainer strong.txtblue
    { font-size: 14px;  }

    #couponCodeContainer strong.txtblue a
    { color: #377ce4; font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; text-decoration: underline; }

    .couponLoading { display: none; }

    #form_couponForm #input_coupon_code
    { border:1px solid #ddd; border-radius: 2px; -moz-border-radius: 2px; -webkit-border-radius: 2px; width: 63%; }

    #applyCoupon.disabled, #removeCoupon.gray-button
    { padding: 6px 0; width: 30%; }

    .offerBlurb { width: 100%;  }

    .offerBlurb .blurbBody { font-size: 13px; }

    td.CartTotal { padding-top: 0; }

    #CartTableFooter  td.CartTotal table td { display: table-cell; text-align: right; }

    #CartTableFooter .ItemConvertedSubtotal td,
    #CartTableFooter .taxTotal td
    {  color: #1b1b1b; font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 600;  }

    #CartTableFooter .ItemTotalAfterDiscount.Cart-Total td,
    #CartTableFooter .ItemTotalAfterDiscount #TotalAmount
    { color: #1ca40d; font-family: 'Open Sans', sans-serif; font-size: 18px; font-weight: 600; }

    td.CartTotal tr.taxTotal td span.vat-label,
    td.CartTotal tr.taxTotal td span#vatTotalAmount,
    td.CartTotal tr.taxTotal td span#taxTotalAmount
    { background: none; }

    .ItemTotalAfterDiscount #TotalAmount
    { margin-right: 0;  }

    #taxTotalCurrency, .ItemConvertedSubtotal #CartTotal #total, #DiscountTotal
    { padding-right: 0; }

    /* Checkout Page - Customer Details Form */

    #login
    { width: 75.5%; padding: 17px 6% 10px 6%; margin: 112px auto 0 auto; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; }

    p.italic_font { width: 87.5%; margin: 6px auto 0 auto; }

    #loginForm, #signup_div
    { width: 100% }

    #customer_details { margin: -15px 0 0 0; position: relative; }

    #customer_details h2.ui-heading
    { width: 100% !important; border-bottom: 0; font-family: 'Open Sans', sans-serif; font-size: 20px; font-weight: bold; color:#1b1b1b;
    position: absolute; top:-33px; left: 50%; margin-left: -50% !important;
    }

    #loginForm #ExistingUserLogin,
    #loginForm #NewUserLogin
    { width: 100%; padding: 0; border-right: 0; }

    #ExistingUserLogin { border-bottom: 1px solid #ddd; }


    .loginform input, .loginform select, .loginform textarea,
    .loginform input[type="text"], .loginform input[type="password"]
    { width: 95%; padding: 0 0 0 5%;
      border: 1px solid #ddd; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px;
      box-shadow: none;
    }

    .CartSection h3
    { font-family: 'Open Sans', sans-serif; font-size: 16px; font-weight: bold; color:#1b1b1b; margin: 0 0 8px 0; }

    .loginform label
    { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 600; }

    #forgotpassword { position: static;  display: block;  margin:10px 0 -7px 0; }

    #existing_submit,
    #new_submit,
    #register_submit_id,
    #pay_gateway_button,
    #pay_offline_button,
    #container .gray-shdow.row-indent .loginform .uiButton
    { background: #0893d8; border: 1px solid #044bbc; height: auto; padding: 11px 0; width: 100%;
      font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400;
        -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25);
        -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25);
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25);

    }

    #existing_submit
    { margin: 0 0 20px 0; }

    .CartSection #NewUserLogin h3
    { margin: 26px 0 -5px 0; }


    /* Checkout page -  Signup Form */

    #signupform { padding: 0; }

    .frmSignupSplit td
    { display: block; width: 100%; }

    .frmSignupSplit .frmField label { font-size: 13px; font-weight: 600; }

    .frmSignupSplit input.frm-field,
    .frmSignupSplit select.frm-select
    { width: 95%; padding: 0 0 0 5%; height: 32px; line-height: 32px;
    border: 1px solid #ddd; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px;
    }

    #input_zip { width: 95% !important; padding: 0 0 0 5%; }

    .frmSignupSplit select.frm-select
    { width: 100%; background: url("/getImage.php?src=images/selectbox-icon.png") no-repeat right 1px center #fff;
    height: 35px; vertical-align: middle; font-size: 14px; font-weight: 400; line-height: 35px;
    }

    #input_phone_cc, #input_mobile_cc { padding: 0; width: 45px; }

    table.frmTable { width: 100%; }

    .rightAlignedForm #input_phone,
    #input_mobile
    { width: 70%; }

    table.frmTable tr td.frmField { padding: 8.5px 0; }

    table tr td.frmCancel { margin: 0; }

    #customer_details .terms-n-conditions, .frmSignupSplit a, table tr td.frmCancel a
    { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; color:#1b1b1b; }

    #customer_details .terms-n-conditions
    { margin: 8px 0 11px 0; }

    .frmSignupSplit a, table tr td.frmCancel a
    { color: #377ce4 }

    .frmCancel a { position: absolute; bottom: 20px; }

    /* Checkout page - Payment Info */

    .error-message
    { width: 77.5%; margin: 0 auto 30px auto; }

    #CartSection_paymentOptions
    { width: 87.5%; margin: 0 auto; padding: 2% 0; position: relative; }

    #CartSection_paymentOptions .pay-sep-or
    { display: none;  }

    #CartSection_paymentOptions .opt-1,
    #CartSection_paymentOptions .opt-2
    { display: block; width: 88%; padding: 0 6%; float: none; }

    #CartSection_paymentOptions .opt-1 { padding-bottom: 10px; border-bottom: 1px solid #ddd; }

    h2.payment-method-heading
    { font-family: 'Open Sans', sans-serif; font-size: 20px; font-weight: bold; color:#1b1b1b;
    width: 87.5%; margin: 44px auto 8px auto !important;
    }

    ul#payment_options_online li.paymentOption { float: none; }

    ul#payment_options_online li.paymentOption label,
    #paymentOption_online label,
    #paymentOption_offline label
    { width: 220px; font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 500; color:#1b1b1b; }

    #pay_gateway_button
    { margin: 7px 0 8px 0; }

    #paymentOption_advance { display: none; }

    #paymentOption_online p, p.italic_font
    {   font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; color:#1b1b1b;  }

    #paymentOption_offline .txt-info { padding: 0; margin: 0 0 17px 0; }

    .CartSection .note2, .CartSection .blue-box { width: 75%; margin: 0 auto; }

    .CartSection .blue-box { margin: 20px auto; }

    /* Show hide contents - Checkout page */

    .CartSection.row-indent
    { position: relative; }

    .show-hide-cart-contents, .show-hide-onlinepay-contents, .show-hide-offlinepay-contents
    { background: url("/getImage.php?src=minus-icon-bg.png") no-repeat; height: 2px; width: 16px;
    position: absolute; right: 6.25%;  top: 64px; cursor: pointer;
    }

    .show-hide-cart-contents.plus, .show-hide-onlinepay-contents.plus, .show-hide-offlinepay-contents.plus
    {   background: url("/getImage.php?src=plus-icon-bg.png") no-repeat; height: 16px; width: 16px;
    top:56px; }

    .show-hide-onlinepay-contents, .show-hide-offlinepay-contents
    {  top: 10px;  right: 6%;  left: auto; }

    .show-hide-onlinepay-contents.plus, .show-hide-offlinepay-contents.plus
    { top:0; right: 6% }

    .CartSection #paymentOption_online h3{ padding: 0; }

    .CartSection { position: relative; }

    .bot-border { background: #b6b6b6; height: 2px; width: auto; left: 6.5%; right: 6.5%; position: absolute;  }

    .show-hide-cart-contents + h2 { border-bottom: 2px solid #b6b6b6;margin-bottom: 25px !important; }

    /* Upsell Modal */

    #select-domain-modal .use-existing select{ width: 100%;  }

    /* Cart Icons

    span.cart-product-icon span#domain-carticon,
    span.cart-product-icon span#subproduct-domain-carticon
    { background-size: 24px 23px;  height: 23px;  width: 24px; }

    span.cart-product-icon span#impressly-carticon
    {  background-size: 19px 19px; width: 19px;  height: 19px;  }

    span.cart-product-icon span#cloudsiteslinuxus-carticon
    { background-size: 17px 16px; width: 17px;  height: 16px;  }


    span.cart-product-icon span#bundles-carticon
    { background-size: 26px 24px; height: 24px;  width: 26px;  }

    span.cart-product-icon span#multidomainhosting-carticon,
    span.cart-product-icon span#linux_hosting-carticon,
    span.cart-product-icon span#multidomainwindowshosting-carticon,
    span.cart-product-icon span#w2k_hosting-carticon
    { background-size: 27px 24px;  height: 24px;  width: 27px;  }

    span.cart-product-icon span#sblite-carticon
    {  background-size: 24px 20px; height: 20px;  width: 24px;  }

    span.cart-product-icon span#vps-carticon,
    span.cart-product-icon span#vpslinuxus-carticon
    { background-size: 21px 19px; height: 19px;  width: 21px;  }


    span.cart-product-icon span#dedicatedservers-carticon,
    span.cart-product-icon span#dedicatedserverslinux-carticon
    { background-size: 25px 26px;  height: 26px;  width: 25px;  }

    span.cart-product-icon span#dedicatedserverswindows-carticon
    {  background-size: 25px 26px; height: 26px;  width: 25px;  }

    span.cart-product-icon span#managedservers-carticon
    { background-size: 23px 22px; height: 22px;  width: 23px;  }

    span.cart-product-icon span#codeguard-carticon
    {  background-size:  21px 20px; height: 20px;  width: 21px;  }

    span.cart-product-icon span#sslcert-carticon
    { background-size: 15px 25px;  height: 25px;  width: 15px;  }

    span.cart-product-icon span#sitelock-carticon
    { background-size: 13px 18px;  height: 18px;  width: 13px;  }

    span.cart-product-icon span#eelite-carticon
    { background-size: 19px 17px; height: 17px;  width: 19px;  }

    span.cart-product-icon span#enterpriseemail-carticon
    { background-size: 25px 16px; height: 16px;  width: 25px;  }

    span.cart-product-icon span#resellerhosting-carticon
    { background-size: 26px 24px;  height: 24px;  width: 26px;  }

    span.cart-product-icon span#resellerwindowshosting-carticon
    { background-size: 28px 22px; height: 22px;  width: 28px;  }

    span.cart-product-icon span#hostgator_hosting_shared_linux_us-carticon
    { background-size: 21px 23px;  height: 23px;  width: 21px;  }

    span.cart-product-icon span#subproduct-domain-carticon
    { background-size: 15px 13px; height: 13px;  width: 15px; }

    span.cart-product-icon span#subproduct-multidomainhosting-carticon
    { background-size: 15px 12px; height: 12px;  width: 15px; }

    span.cart-product-icon span#subproduct-eelite-carticon
    { background-size: 10px 9px; height: 9px;  width: 10px;  }

    span.cart-product-icon span#subproduct-ssl-carticon
    { background-size: 8px 14px; height: 14px;  width: 8px;  }

    span.cart-product-icon span#subproduct-wbuilder-carticon
    { background-size: 14px 11px; height: 11px;  width: 14px; }

    span.cart-product-icon span#hostgator_hosting_dedicatedserver_linux_us-carticon
    { background-size: 19px 17px;  height: 17px;  width: 19px;  }
    */

    .ItemSubTitle { margin-bottom: 0 !important; }

    .sub-products .cartItem-product-detail { width: 78%; }

    .CartDuration .dropdown-value 
    { background: url("/getImage.php?src=checkout-year-dd-mobile.png") no-repeat right center;  }

    #CartTable td.CartSubTotal { background: transparent; }

    .add-auto-renewal-cart { padding-bottom: 3px; }

    .ItemTotalAfterDiscount.Cart-Total .TotalAmountlabel, .ItemTotalAfterDiscount.Cart-Total #TotalAmount
    { padding-bottom: 0; margin: 0 0 -6px 0; }

    .ItemTotalAfterDiscount td { border-color: #ccc; }

    .loginform #rememberme input { display: inline-block; vertical-align: middle; }

    table.frmTable tr:first-child td.frmField { padding-top:0; }

    .frmSignupSplit .frmField label#label_phone {  margin: -14px 0 0 0; }

    .action-required-wrapper { width: 77.5%; margin: 20px auto; position: relative }

    .action-required-wrapper #action_required_table td,
    .action-required-wrapper #action_required_table th
    { width: 45%; word-wrap: break-word; display: inline-block }

    .intro-text.gray-box .ui-space { display: none; }

    .ic-server, .ic-user, .privacy_lock_wrapper .ic-privacy { background-size: 13px 13px !important; height: 13px; width: 13px; }

    .cart-userinfo-bar .v-bar { height: 14px; }

    .ic-user { margin: 5px 2px 0 0 }

    .ic-server { margin: 0px 2px 0px 1px; }

    .privacy_lock_wrapper .ic-privacy { margin: 0px 5px 0px 4px; }


    ul#payment_options_online { margin: 3px 0 -8px 0; padding: 0; }

    ul#payment_options_online li#payment_options_custom input,
    ul#payment_options_online li.paymentOption input
    { margin: 3px 3px 0 0; }

    ul#payment_options_online li.paymentOption label,
    #paymentOption_online label, #paymentOption_offline label
    { padding: 0 0 15px 0; }

    #pay_offline_button{ margin: 0 0 -11px 0; }

    #CartTable tr.CartItemRow td .invoiced
    { color:#222; font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400; }


    /*#contact_list_modal .modal_content
    { width: 94% !important; margin-left: -48% !important; }

    #collapse-nameserver table { width: 85%; }

    .cart-modal-tab-content .new_contact_col { width: 260px; }

    .cart-modal-tab-content .new_contact_col table { width: 90%; }*/

    .cart-modal-tab-content .new_contact_col .view-contact-details-field
    { min-width: 0; display: block; margin: 0; min-height: 50px; width: 100% }

    #contact_list_modal .modal_content
    { width: 87.5% !important; margin-left: -45.75% !important; }

    .cart-modal-tab-content .new_contact_col .view-contact-details-field {  }

    #contact_list_modal .modal_content h3.heading { margin: 0 auto; padding: 28px 0 10px 0; font-size: 16px; width: 90%; }

    .cart-modal-tab-content .new_contact_details .new_contact_col { width:90%; }

    .new_contact_details td, .new_contact_details td label, .new_contact_details td span
    { display: block; margin: 0; }

    #tab2-cont label.frm-label, #tab2-cont input.frm-field { font-size: 12px !important; }

    .cart-modal-tabs li.active-tab
    { border: 0 !important;  border-top:2px solid #308ad0 !important;
      border-radius: 0; -moz-border-radius: 0; -webkit-border-radius: 0;
    }

    .cart-modal-tabs li { width: 80%; }


    #tab2-cont input.frm-field{width: 100px; max-width: 100px; }

    #collapse-nameserver { margin-left: -15px; }

    #collapse-nameserver table { width: 85%; }

    #collapse-nameserver table td{width: 45%; max-width: 45%; }

    /* Order Success Page */

    #maincontent
    { max-width: 280px !important; width: 280px !important;  }

    #CartSection-wrapper { width: 280px; max-width: 280px; padding: 0; }

    #CartSection-wrapper .note2.green-bar, #CartSection-wrapper .note2.green-bar a { font-size: 14px; text-align: center; }

    #CartSection-wrapper .note2.green-bar a { display: block;  text-align: center;  margin: 5px auto;  }

    .modal_content .responsivediv { padding: 25px 0 15px 0; margin: 0 auto; width: 150px; }


    /* Login / Signup Menu link */

    #container.row-indent { width: 100%; padding: 0; }

    #container .gray-shdow.row-indent
    { background: #fff; width:87%;  padding: 0 6.5%; }

    #container .gray-shdow.row-indent .ui-sub-heading,
    #container .gray-shdow.row-indent #signup .ui-heading
    { font-family: 'Open Sans', sans-serif; font-size: 20px; color:#1b1b1b; text-align: center; font-weight: bold;}

    #container .gray-shdow.row-indent h3.ui-sub-heading { margin: 0 0 10px 0; }

    #container .gray-shdow.row-indent .lfloat, #container .gray-shdow.row-indent .loginform
    { width: 100% !important; }

    #container .gray-shdow.row-indent .loginform { text-align: center; }

    #container .gray-shdow.row-indent .loginform  #cust-login-form
    { border:1px solid #ddd;  border-radius: 3px; -webkit-border-radius: 3px; -webkit-border-radius: 3px;
      margin: 9px 0 44px 0;
    }

    #container .gray-shdow.row-indent .loginform input,
    #container .gray-shdow.row-indent .loginform select,
    #container .gray-shdow.row-indent #signup select,
    #container .gray-shdow.row-indent .loginform textarea,
    #container .gray-shdow.row-indent .loginform input[type="text"],
    #container .gray-shdow.row-indent .loginform input[type="password"]
    { width: 80.5%; padding: 0 0 0 5%; }

    #container .gray-shdow.row-indent .loginform label,
    #container .gray-shdow.row-indent div#rememberme
    { width: 86%;  text-align: left;  padding: 0 7%; }

    #container .gray-shdow.row-indent .loginform label#label_email
    { margin: 17px 0 5px 0; }


    #container .gray-shdow.row-indent .loginform input#existing_remember,
    #container .gray-shdow.row-indent .loginform label#label_rememberme
    { width: auto; padding: 0; display: inline-block; }

    #container .gray-shdow.row-indent .loginform .uiButton
    { margin: 25px auto 20px auto; border-radius: 3px; -webkit-border-radius: 3px; -webkit-border-radius: 3px; width: 87%; }


    /* Signup */

    #container .gray-shdow.row-indent #signup form#sign > table
    { width: 100%; border: 1px solid #dddddd;
      border-radius: 3px; -webkit-border-radius: 3px; -webkit-border-radius: 3px;  padding: 15px 0 10px 0;  display: block;
    }

    #container .gray-shdow.row-indent #signup td{ display: block; width: 100%; }

    #container .gray-shdow.row-indent #signup .info-small { padding: 5px 0 0 7% }

    #container .gray-shdow.row-indent #signup input#select_city,
    #container .gray-shdow.row-indent #signup input#input_zip
    { padding: 0 0 0 5%; width: 80.5% !important; }

    #container .gray-shdow.row-indent #signup select
    { background: url("/getImage.php?src=images/selectbox-icon.png") no-repeat right #fff;
      height: 35px; line-height: 35px; border-radius: 3px; -webkit-border-radius: 3px; -webkit-border-radius: 3px;
      width: 86.2%;
    }

    #container .gray-shdow.row-indent #signup label#label_state{ margin: 20px 0 0 0; }

    #container .gray-shdow.row-indent #signup label#label_country,
    #container .gray-shdow.row-indent #signup label#label_phone
    {  margin: -7px 0 5px 0; }

    #container .gray-shdow.row-indent .loginform label[for='input_phone_cc'].checked,
    #container .gray-shdow.row-indent .loginform label[for="input_mobile_cc"].checked
    { display: none !important; }


    #container .gray-shdow.row-indent #signup input#input_phone_cc,
    #container .gray-shdow.row-indent #signup input#input_mobile_cc
    { width: 13%; padding: 0; text-align: center; display: inline-block; }

    #container .gray-shdow.row-indent #signup input#input_phone,
    #container .gray-shdow.row-indent #signup input#input_mobile
    { width: 62.5%; }

    #container .gray-shdow.row-indent .loginform#login_right .error-message
    {
        border: 0;
        background: none;
        font-family: 'Open Sans', sans-serif;
        font-size: 13px;
        font-weight: 400;
        margin: 0;
    }

    #container .gray-shdow.row-indent .termnconditions{ font-size: 13px; line-height: 20px; margin: 15px 0 0 0; }

    #container .gray-shdow.row-indent #sub_button
    { margin: 0 0 9px 0; width: 100%; }

    #container .gray-shdow.row-indent #pto { padding-left:0; }

    #container .gray-shdow.row-indent #pto a
    { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; text-decoration: underline; }

    #container .gray-shdow.row-indent #signup .ui-heading { text-align: left;  margin: -25px 0 15px 0; }


    /* Forgot Password */

    form[name="forgotpassword"]
    {  border: 1px solid #e0e0e0;  border-radius: 6px;  -moz-border-radius: 6px;  -webkit-border-radius: 6px; padding: 30px;  }

    form[name="forgotpassword"] label.frm-label
    {  font-family: 'Open Sans', sans-serif;  font-size: 13px;  font-weight: 600; color:#1b1b1b; }

    form[name="forgotpassword"] input[type="text"]
    {
        border: 1px solid #ddd; box-shadow: none;
        border-radius: 2px;  -moz-border-radius: 2px;  -webkit-border-radius: 2px;
        width: 100%;  padding: 0 0 0 5%;  height: 36px;  line-height: 36px;
    }

    form[name="forgotpassword"] input[type="submit"]
    {  background: #0099d5;  padding: 9px 20px; border: 1px solid #1c58d6 !important;
        color: #fff !important;  cursor: pointer;
        font-family: 'Open Sans', sans-serif;  font-size: 15px;  font-weight: 400;  margin: 15px 0 0 0;
        border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 2px 2px rgba(0, 0, 0, 0.2);
        -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 2px 2px rgba(0, 0, 0, 0.2);
        -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 2px 2px rgba(0, 0, 0, 0.2);
        text-shadow: #0232b3 0 1px 0;
        outline: none;
    }




    /* GDPR */

    #consent_div { text-align: left; margin: 15px 0 0 0; }

    #container .gray-shdow.row-indent .loginform #consent_div label.termnconditions
    {  margin: 0; padding: 0; white-space: normal; width: 87%; }

    #container .gray-shdow.row-indent .loginform .uiButton.disabled-uiButton
    { background: #d7d7d7;  border: 1px solid #c7c7c7 !important;  color: #898989 !important; text-shadow: none; outline: none; cursor: no-drop; box-shadow: none; }


    #container .gray-shdow.row-indent .termnconditions label
    { font-family: 'Open Sans', sans-serif;  font-size: 13px;  font-weight: 400; color:#1b1b1b; }

    #container .gray-shdow.row-indent p.termnconditions label[for="tnc_agree"]
    {  display: inline-block;  width: 85%; font-family: 'Open Sans', sans-serif;  font-size: 13px;  font-weight: 400;  color: #1b1b1b; white-space: normal; line-height: 20px;
        vertical-align: top;
        text-align: left;
        padding: 0;
        margin: 0;
    }


}



/* Tablet View (above 768px and below 1000px) */

@media screen and (min-width: 768px) and (max-width: 1000px)
{
    body { min-width: 768px; }

    #page-header .new-header-style { padding: 0 2.6%; }

    .mobile-menu { width:320px; max-width: 320px; }

    .homepage-product-details span.price-symbol { margin: -3px 0 0 0 !important; }

    #page-header h1#branding .logo-cont a { display: block; width: 250px; }

    /* Checkout Page */

    .CartSection { width: 94.8%; margin: 0 auto; }

    .CartSection.row-indent { padding: 25px 2.6% 10px 2.6%; }

    .cartItem-product-detail { width:300px; max-width: 300px;  }

    #loginForm, #signup_div { width: 100%; }

    .CartSection h2.ui-heading
    { font-size: 22px; line-height: 32px; font-family: 'Open Sans', sans-serif;  padding: 0 0 8px 0;  margin: -11px 0 0 0;font-weight: bold;  }

    #CartTable.shop-cart-table th { font-size: 18px; font-weight: 600; }

    #CartTable.shop-cart-table th:first-child { padding-left: 0; }

    #CartTable.shop-cart-table td
    { padding: 20px 10px 15px 0; }

    .CartSection .ItemTitle, #couponCodeContainer strong.txtblue { font-size: 16px; font-weight: 600; }

    .CartSection .ItemTitle { width: 195px;  white-space: nowrap;  overflow: hidden;  text-overflow: ellipsis; }

    .CartSubTotal p { font-size: 16px; font-weight: 400;  }

    .CartTotal p, .ItemConvertedSubtotal #CartTotal #total, .ItemConvertedSubtotal #CartTotal
    { font: 600 16px 'Open Sans', sans-serif; }

    .ItemTotalAfterDiscount.Cart-Total .TotalAmountlabel,
    .ItemTotalAfterDiscount.Cart-Total #TotalAmount
    { font-family: 'Open Sans', sans-serif; font-size: 20px; font-weight: 600; color: #00a22a; padding-bottom: 23px; }

    #customer_details .CartSection { padding: 30px 0; width: 100%; }


    .loginform input
    { width: 278px;  }

    #loginForm #ExistingUserLogin,
    #loginForm #NewUserLogin
    { width: 45%; }

    #customer_details h2.ui-heading,
    #signupform  h2.ui-heading
    { background: transparent; margin: -65px auto 0; width: 75%; }

    .CartSection h3 { font-family: 'Open Sans', sans-serif; font-size: 18px; font-weight: 600; }

    .loginform label, #container .gray-shdow.row-indent label
    { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 600; }


    /* Login / Signup */

    #container .gray-shdow.row-indent
    { background: #fff; border: 1px solid #e0e0e0; padding: 30px; margin: 30px auto;
      border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px;
    }

    #container .gray-shdow.row-indent .lfloat
    { width: 40% !important; margin: 0 0 0 5%; padding: 0 0 0 5%;  border-left: 1px solid #e0e0e0; min-height: 280px; }

    #container .gray-shdow.row-indent .lfloat:first-child
    { padding: 0 0 0 10px; border: 0; width: 45% !important; margin: 0; }

    #container .gray-shdow.row-indent .ui-sub-heading,
    #container .gray-shdow.row-indent .ui-heading
    { margin: 0; font-family: 'Open Sans', sans-serif;  font-size: 18px;  font-weight: 600; color: #1b1b1b;  }

    #container .gray-shdow.row-indent .loginform { padding-top:0; width:100%; }

    #container .gray-shdow.row-indent input[type="text"],
    #container .gray-shdow.row-indent input[type="password"],
    #container .gray-shdow.row-indent select
    { width: 95%; padding: 5px 0 5px 5%; line-height: 35px; height: 35px; }

    #container .gray-shdow.row-indent #forgotpassword
    { top:0; float: right; left: auto; }

    #container .gray-shdow.row-indent #label_rememberme { font-weight: 400; vertical-align: top; }

    #container .gray-shdow.row-indent .uiButton,
    form[name="forgotpassword"] input[type="submit"]
    {  background: #0099d5;  padding: 9px 20px; border: 1px solid #1c58d6 !important;
        color: #fff !important;  cursor: pointer;
        font-family: 'Open Sans', sans-serif;  font-size: 15px;  font-weight: 400;  margin: 15px 0 0 0;
        border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 2px 2px rgba(0, 0, 0, 0.2);
        -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 2px 2px rgba(0, 0, 0, 0.2);
        -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 2px 2px rgba(0, 0, 0, 0.2);
        text-shadow: #0232b3 0 1px 0;
        outline: none;
    }

    #container .gray-shdow.row-indent #signup td,
    #container .gray-shdow.row-indent #signup td table td,
    #container .gray-shdow.row-indent #signup td table td:first-child,
    #container .gray-shdow.row-indent #signup td  .info-small
    { padding: 10px 0; display: inline-block; vertical-align: top;  width: 340px !important; }

    #container .gray-shdow.row-indent #select_city,
    #container .gray-shdow.row-indent #input_zip,
    #container .gray-shdow.row-indent #country,
    #container .gray-shdow.row-indent #stateSelect,
    {
        width: 139px !important;
    }

    #container .gray-shdow.row-indent #signup td table td,
    #container .gray-shdow.row-indent #signup td table td:first-child,
    #container .gray-shdow.row-indent #signup td table td label
    { display: inline-block;  padding: 0;  width: 150px !important;  margin:5px 0;  }


    #container .gray-shdow.row-indent #signup #label_state,
    #container .gray-shdow.row-indent #signup #label_zip,
    #container .gray-shdow.row-indent #signup label[for="input_zip"].error,
    #container .gray-shdow.row-indent #signup label[for="stateSelect"].error
    { margin-left: 20px; }

    #container .gray-shdow.row-indent #signup #input_phone,
    #container .gray-shdow.row-indent #signup #input_mobile
    { width: 230px !important; }

    #container .gray-shdow.row-indent select
    { background: url(/getImage.php?src=images/selectbox-icon.png) no-repeat right #fff; line-height: 35px; height: 35px;
      border: 1px solid #ddd;  border-radius: 2px;  -moz-border-radius: 2px;  -webkit-border-radius: 2px; padding: 0 10px;
    }

    .loginform label, #container .gray-shdow.row-indent label.error { font-weight: 400; font-size: 12px; }

    #container .gray-shdow.row-indent .error-message
    { background: none; border:0; font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; margin: 2px 0 15px 0;  }

    #container .gray-shdow.row-indent #signup input[type="text"],
    #container .gray-shdow.row-indent #signup input[type="password"]
    {
        border: 1px solid #ddd;
        border-radius: 2px;  -moz-border-radius: 2px;  -webkit-border-radius: 2px;
        padding: 0 0 0 10px;  width: 290px;  height: 36px;  line-height: 36px;
    }

    #container .gray-shdow.row-indent #signup #input_phone_cc,
    #container .gray-shdow.row-indent #signup #input_mobile_cc
    {  width: 50px;  text-align: center; padding: 0;  }

    /* Forgot Password */

    form[name="forgotpassword"]
    {  border: 1px solid #e0e0e0;  border-radius: 6px;  -moz-border-radius: 6px;  -webkit-border-radius: 6px; padding: 30px;  }

    form[name="forgotpassword"] label.frm-label
    {  font-family: 'Open Sans', sans-serif;  font-size: 14px;  font-weight: 600; white-space: nowrap;   }

    form[name="forgotpassword"] input[type="text"]
    {
        border: 1px solid #ddd; box-shadow: none;
        border-radius: 2px;  -moz-border-radius: 2px;  -webkit-border-radius: 2px;
        padding: 0 0 0 10px;  width: 290px;  height: 36px;  line-height: 36px;
    }

    .italic_font
    { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 200; }

    td.CartTotal tr.taxTotal td span.vat-label,
    td.CartTotal tr.taxTotal td span#vatTotalAmount,
    td.CartTotal tr.taxTotal td span#taxTotalAmount
    { background: transparent; }

    /*.pp_in_cart .rfloat { margin-right: 10px; }*/

    .ItemSubTitle { margin-bottom: 0 !important; }

    #CartSection_paymentOptions .opt-1#paymentOption_online,
    #CartSection_paymentOptions .opt-2#paymentOption_offline
    { width: 43.5%; padding: 0 0 0 4%; float: left; }

    #CartSection_paymentOptions .opt-2#paymentOption_offline { margin: 0 0 0 2.5%; }

    .pay-sepr { background-position: 50% 0; }

    .pay-sep-or span { left: 48%;  }

    #existing_submit, #new_submit, #pay_gateway_button, #pay_offline_button, #register_submit_id
    { background: #0099d5; border: 1px solid #0051b6; padding: 9px 30px; }

    #paymentOption_advance,
    #CartSection_paymentOptions .white-bg.div-spacer,
    .intro-text.gray-box .ui-space,
    #paymentOption_online p:last-child
    { display: none; }

    .intro-text.gray-box > h2.ui-heading:first-child
    { color:#222; font-family: 'Open Sans', sans-serif; font-size: 18px; font-weight: 600; }

    .intro-text.gray-box > h2.ui-heading
    { padding-bottom: 0; }

    #ExistingUserLogin { position: relative; }

    #ExistingUserLogin .pay-sep-or span {
        height: 25px; position: absolute;  padding: 3px;  text-align: center;  line-height: 25px;
        border: 1px solid #d6d6d6;  border-radius: 50%;  -moz-border-radius: 50%;  -webkit-border-radius: 50%;
        color: #ccc;  font-family: 'Open Sans', sans-serif;  font-size: 14px;  font-weight: 400;
        left: auto; right: -18px;
    }

    #CartSection_paymentOptions .pay-sep-or span { left: 48%; }

    .frmSignupSplit { margin: 13px 0 0 0; }

    .frmSignupSplit td { vertical-align: top; }

    .frmSignupSplit input.frm-field, .frmSignupSplit select.frm-select
    { border: 1px solid #ddd; border-radius: 2px; -moz-border-radius: 2px; -webkit-border-radius: 2px;
      padding: 0 0 0 10px; width: 290px; height: 36px; line-height: 36px;
    }

    .frmSignupSplit select.frm-select
    { background:url("/getImage.php?src=images/selectbox-icon.png") no-repeat 128px #fff; }

    .frmSignupSplit input.frm-field#select_city,
    .frmSignupSplit input.frm-field#input_zip
    { width: 139px; }

    .frmSignupSplit select.frm-select#country,
    .frmSignupSplit select.frm-select#stateSelect
    { width: 150px; }

    .frmSignupSplit .frmField label#label_phone
    { padding-top:7px; }

    #label_zip, #label_state  { margin: 0 0 4px 20px; }

    #input_zip, #stateSelect { margin: 0 0 0 20px; }

    #signupform { padding: 0 30px; }

    .frmSignupSplit .frmField label,
    table tr td.frmCancel a, .frmSignupSplit a,
    .frmSignupSplit td p.terms-n-conditions
    { font-family: 'Open Sans', sans-serif;  font-size: 14px;  font-weight: 600; }

    table tr td.frmCancel a, .frmSignupSplit a, .frmSignupSplit td p.terms-n-conditions { font-weight: 400; }

    .italic_font
    { font-family: 'Open Sans', sans-serif;  font-size: 13px;  font-weight: 200; margin: 5px 0 0 0; }

    .item-close a { background-size: 12px 12px; height: 12px; margin: 3px 0 0 0; width: 12px; }

    .ItemTotalAfterDiscount td { border-color: #ccc; }

    #form_couponForm #input_coupon_code
    { border:1px solid #ddd; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px;
      box-shadow: none;
    }

    #login.CartSection
    { position: relative; }

    #customer_details h2.ui-heading, #signupform  h2.ui-heading
    { position: absolute; top:17px;  margin: -65px auto 0 -37.5% !important;  left: 50%; }

    #ExistingUserLogin.loginform, #NewUserLogin.loginform
    { padding-top:0; }

    .CartSection #ExistingUserLogin h3,
    .CartSection #NewUserLogin h3
    { margin: -4px 0 20px 0; }


    .loginform input[type="text"], .loginform input[type="password"]
    {  border:1px solid #ddd; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px;
       box-shadow: none;
    }

    .loginform input[type="text"]{  margin: 0 0 2px 0; }

    h2.payment-method-heading { margin: 25px 0 10px 0 !important }

    #paymentOption_online { margin: -4px 0 0 0; }

    #paymentOption_online:after
    {   content: "";  height: 104%;  width: 1px;  background: #ccc;
        display: block;  position: absolute;  top: -10px;  right: -18px;
    }
    .pay-sepr { background: none; border-radius: 2px; -webkit-border-radius: 2px; -moz-border-radius: 2px; }

    .pay-sep-or span { z-index: 999; }

    #paymentOption_online h3 { margin: -13px 0 0 0; }

    #paymentOption_offline h3 { margin-top:-3px; }

    ul#payment_options_online li.paymentOption { margin: 0 0 15px 0 }

    ul#payment_options_online li.paymentOption label
    { vertical-align: top; margin: 0; padding: 0; }

    ul#payment_options_online { margin: 8px 0 0 0; }

    ul#payment_options_online li.paymentOption input,
    ul#payment_options_online li#payment_options_custom input
    { vertical-align: top; margin: 5px 5px 0 0; }

    ul#payment_options_online li#payment_options_custom input { margin: 2px 5px 0 0;  }

    .paymentOption .txt-info { font-size: 14px; line-height: 21px; }

    #pay_offline_button { margin: 16px 0 0 0; }

    .invoiced, .add-auto-renewal-cart { width: 90px; }

    .add-auto-renewal-cart { margin: 0 0 0 17px; }

    .intro-text.gray-box { margin: -25px auto; }

    .intro-text.gray-box .ui-space { height: 10px; display: block; }

    #CartTable tr.CartItemRow td .invoiced
    { background: none; color:#222; font-family: 'Open Sans', sans-serif; font-size: 12px; font-weight: 400; padding: none; }


    #contact_list_modal .modal_content
    { width: 94% !important; margin-left: -48% !important; }

    #collapse-nameserver table { width: 85%; }

    .cart-modal-tab-content .new_contact_col { width: 620px; }

    .cart-modal-tab-content .new_contact_col table { width: 90%; }

    .cart-modal-tab-content .new_contact_col .view-contact-details-field
    { min-width: 0; width: 310px }

    /* Order Success Page */

    #maincontent
    { max-width: 768px !important; width: 768px !important;  }

    #CartSection-wrapper { width: 728px; max-width: 728px; padding: 0; }

    #CartSection-wrapper .note2.green-bar, #CartSection-wrapper .note2.green-bar a { font-size: 14px; }

    .modal_content .responsivediv { padding: 25px 0 15px 0; margin: 0 auto; width: 250px; }



    /* GDPR */

    #container .gray-shdow.row-indent #consent_div label.termnconditions
    {
        font-family: 'Open Sans', sans-serif;
        font-size: 13px;
        font-weight: 400;
        color: #1b1b1b; white-space: normal; width: 65%;
    }

    #container .gray-shdow.row-indent p.termnconditions label[for="tnc_agree"]
    {  display: inline-block;  width: 85%; font-family: 'Open Sans', sans-serif;  font-size: 13px;  font-weight: 400;  color: #1b1b1b; white-space: normal; }


}

/* Mobile View & Tablet */

@media only screen and (min-width: 320px) and (max-width: 767px), (min-width: 768px) and (max-width: 1000px)
{

                                    /* Hosting & Domain Focused */

    /* Common */

    .homepage-new-gtld-link,
    .bundles-hp-blurb-text .green-text,
    .promoblurb-container,
    .homepage-banner-rhs,
    .online-business-icon,
    .homepage-product-details ul,
    .homepage-product-details span.start-at,
    .hosting-homepage-banner-content ul,
    .domain-hp-tld-list
    { display: none; }

    input[type=text], input[type=password] {
        /* Remove First */
        -webkit-appearance: none !important;  -moz-appearance: none;  appearance: none;
        background-clip: padding-box;
    }

    /* Banner */

    .homepage-banner { padding: 24px 0 66px 0; position: relative; z-index: 9; }

    .homepage-banner h1, .homepage-content h1, .hosting-homepage-banner-content h1, .domain-hp-content h1, .domain-hp-free-addons h1
    { font-size: 20px; line-height: 27px; padding: 0 0 3px 0; }

    .homepage-banner form
    { width: 87.5%; margin: 25px auto 0 auto; }

    .homepage-banner .green-submit-button
    { font-size: 15px;  padding: 12px 0;  width: 25%;
      border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0; -webkit-border-radius: 0 3px 3px 0;
    }

    .homepage-container, .homepage-banner, .homepage-products, .hosting-homepage-banner-content
    { width: 100%; }

    .add-top-space, .add-top-space2, .hosting-homepage-banner, .new-homepage-wrapper.domain-focused-homepage {
        border-top:1px solid #e6e6e6;
        -moz-box-shadow:    inset 0 8px 9px -6px #e6e6e6;
        -webkit-box-shadow: inset 0 8px 9px -6px #e6e6e6;
        box-shadow:        inset 0 8px 9px -6px #e6e6e6;
    }

    .new-homepage-wrapper-dark-banner.add-top-space
    {
        -moz-box-shadow:    inset 0 8px 9px -6px rgba(230, 230, 230, 0.5);
        -webkit-box-shadow: inset 0 8px 9px -6px rgba(230, 230, 230, 0.5);
        box-shadow:        inset 0 8px 9px -6px rgba(230, 230, 230, 0.5);
    }

    .homepage-banner { min-height: 282px; }

    .bundles-hp-blurb-content { border: 0; width: 100%; }

    .homepage-banner-lhs
    { float: none; margin: 0 auto; text-align: center; width: 100%; }

    .homepage-banner input[type="text"],
    .domains-homepage-container form input[type="text"]
    { font-size: 15px;  height: 44px;  line-height: 44px;  width: 67%;  padding: 0 10px 0 5%;
      border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px;
    }

    .homepage-banner input[type="text"] { line-height: 45px; }

    .new-homepage-wrapper, .new-homepage-wrapper-dark-banner { position: relative; }

    .bundles-hp-blurb
    { background: transparent !important; position: absolute;  left: 50%; margin-left: -140px;
      text-align: center;  top:130px; width: 280px; z-index: 99;
    }

    .bundles-hp-blurb h1 { font-size: 16px; line-height: 21px; width: 100%; text-align: center; }

    .bundles-hp-blurb-text, .bundles-hp-blurb-text ul { width: 100%; text-align: center; }

    .bundles-hp-blurb-text {  padding: 15px 0; }

    .bundles-hp-blurb-products li
    { padding: 0; color: #474747; font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 600;
      text-align: center; vertical-align: top; width: 23.5%; word-wrap: break-word;  }

    .bundles-hp-blurb-products li.plus-parent
    { vertical-align: middle; width: 10%; }

    .bundles-hp-blurb-products li .plus-icon { font-size: 50px; margin: 20px 0 0 0; }

    .buy-bundles-plans
    { display: block; position: static; margin: 20px auto 0 auto;
      font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 600;  width: 67%; z-index: -1; }

    .homepage-banner-rhs-ch { display: none; }

    .new-homepage-wrapper-dark-banner .bundles-hp-blurb-products li .bundles-domainname-icon,
    .bundles-hp-blurb-products li .bundles-domainname-icon
    { width: 30px; }


    /* Banner with combo offers */
    .new-homepage-wrapper.add-top-space,
    .new-homepage-wrapper-dark-banner.add-top-space
    {  background-position: center -40px; min-height: 380px; }

    .new-homepage-wrapper-dark-banner.add-top-space { background-position: center -27px;  }

    .new-homepage-wrapper.add-top-space .banner-shadow,
    .new-homepage-wrapper-dark-banner.add-top-space .banner-shadow
    { top:413px; }

    .bundles-hp-blurb .green-txt .WebRupee { padding-right: 4px; }


    /* Banner without combo offers */
    .new-homepage-wrapper.add-top-space2, .new-homepage-wrapper-dark-banner.add-top-space2
    { background-position: center -183px; }

    .new-homepage-wrapper-dark-banner.add-top-space2 { background-position: center -170px;  }

    .new-homepage-wrapper.add-top-space2 .banner-shadow,
    .new-homepage-wrapper-dark-banner.add-top-space2 .banner-shadow
    { top:270px;  }

    .add-top-space2 .homepage-banner { min-height: 180px; }

    .homepage-banner-lhs.content-centered
    { width: 100%; }

    .homepage-banner .content-centered input[type="text"]
    { width: 70%; }



    /* Page Content */

    .homepage-product-details, .homepage-products div.hp-no-margin
    { display: block; margin: 0 auto 20px auto; height: 182px; }

    .highlight-block, .white-block { width: 50%; overflow: hidden; }

    .highlight-block, .white-block, .homepage-pricing
    { display: inline-block; *display:inline; *position: relative; vertical-align: top; zoom:1;
      border:1px solid #e4e4e4; border-left:0; margin: 0;
    }

    .white-block { height: 182px; border-left:1px solid #e4e4e4; margin-right:-3px; }

    .homepage-product-details .product-icon { height: 94px; }

    .homepage-pricing { margin: 0 0 0 -3px; min-height: 94px; padding: 37.5px 3%; text-align: right; width: 40%; }

    .homepage-product-details { position:relative; width: 95%; border:0;  }

    .homepage-product-details h2 { font-size: 16px; }

    .homepage-product-details span.product-tagline { font-size: 12px; }

    .homepage-product-details .green-submit-button
    { font-size: 13px; float: right; margin: 18px 0 0 0; padding: 6px 20px; }

    .homepage-product-details span.price-value, .homepage-product-details span.price-symbol
    { color:#000; font-size: 20px; }

    .homepage-pricing .price-symbol .WebRupee { font-size: 18px; }




                                                    /* Hosting Focused */


    .hosting-homepage-banner
    { background: url("/getImage.php?src=/home-hosting-mobile-bg.png") repeat-x center 10px; padding: 24px 0 0 0;  }

    .hosting-homepage-banner-content .subtext { font-size: 14px; color:#000; }

    .hosting-homepage-banner .hosting-plan-icon, .hosting-homepage-dark-banner .hosting-plan-icon
    { background-size: 280px 70px; height: 70px; width: 280px; margin: 57px auto 0 auto; background-position: center bottom; }

    .hosting-homepage-banner-content { position: relative; z-index: 9;}

    .hosting-plans-price { margin: 20px 0 0 0; }

    .hosting-plans-price .homepage-pricing
    { border:0; position: static; min-height: auto; padding: 0; text-align: center; width: 100%; }

    .hosting-plans-price .homepage-pricing span.start-at
    { font-family: 'Open Sans', sans-serif; font-size: 16px; font-weight: bold; color:#000; display: block; }

    .hosting-plans-price .homepage-pricing span.price-symbol,
    .hosting-plans-price .homepage-pricing span.price-value,
    .hosting-plans-price .homepage-pricing span.price-per
    { font-size: 20px; font-family: 'Open Sans', sans-serif; font-weight: bold; color: #55ca41;
      display: inline; margin:  0 !important; vertical-align: middle !important;
      padding: 0 !important; line-height: 30px !important;
    }

    .hosting-homepage-banner-content .green-submit-button
    { position: absolute; top:124px; font-size: 15px; height: 35px; width: 120px; left: 50%; margin: 0 0 0 -60px; padding: 0; z-index: 99; cursor:pointer; }

    .hosting-homepage-banner .web-hosting-supports, .hosting-homepage-dark-banner .web-hosting-supports {
        height: 45px;
        background-size: 90% 50%;
    }

    .hosting-homepage-container .homepage-content { margin: 24px auto 30px auto; }

    .search-domain-bar form { margin: 0 auto; width: 95%; }

    .search-domain-bar input[type="text"] { width: 70%; font-size: 14px; height: 44px; line-height: 44px; }

    .search-domain-bar .green-submit-button { width: 24%; font-size: 15px; height: 46px; padding: 0; }




                                            /* Domain Focused */


    .domain-homepage-banner { padding: 95px 0 0 0; }

    .domains-homepage-container span.subtext { font-size: 14px; }

    .domains-homepage-container form .green-submit-button { width: 24%; }

    .domains-homepage-container .homepage-banner { height:320px; }

    .domains-banner-shadow { height: 10px; top:325px; z-index: 999; }

    .new-homepage-wrapper .domains-banner-shadow { top: 320px; }

    .new-homepage-wrapper.domain-focused-homepage {
        background-position: center -123px;
    }

    .domain-hp-content
    { margin: 0 auto;  width: 100%;  position: relative;  background: #fff;  padding: 11px 0 0 0; }

    .domain-hp-content a.more-tlds, .domain-hp-content a.more-tlds:hover
    { color: #2385e8; font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; margin-right: 20px; }

    .domain-hp-free-addons h1, .domain-hp-content h1 { font-size: 17px; }

    .domain-hp-free-addons h1
    { margin: -25px 0 22px 0; }

    .domain-hp-content .lowest-price-tlds-list
    { border:1px solid #ddd; border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px;
      margin: 8px auto 0 auto; max-width: none; width: 87.5%; }

    .domain-hp-content .lowest-price-tlds-list li
    { border:0; border-radius: 0; margin:0 0 0 -4px; width: 139px; }

    .domain-hp-content .lowest-price-tlds-list li:nth-child(1)
    { border-width: 0 1px 1px 0; border-style: solid; border-color: #ddd; }

    .domain-hp-content .lowest-price-tlds-list li:nth-child(2)
    { border-width: 0 0 1px 0; border-style: solid; border-color: #ddd; }

    .domain-hp-content .lowest-price-tlds-list li:nth-child(3)
    { border-width: 0 1px 0 0; border-style: solid; border-color: #ddd; width: 144px; }

    .domain-hp-content .lowest-price-tlds-list li span.tld-name
    { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: normal; }

    .domain-hp-content .lowest-price-tlds-list li span.discounted-price
    { font-size: 13px; }

    .domain-hp-free-addons ul
    {  margin: 0 auto; max-width: 320px; width: 320px; }

    .domain-hp-free-addons ul li
    {   font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400;
        display: block; margin: 0 0 20px 20px !important; text-align: left; }


}

/* Mobile View (above 320px and 767px */

@media screen and (min-width: 320px) and (max-width: 767px)
{
    .bundles-hp-blurb h1 { margin: 48px 0 15px 0; }

    .bundles-hp-blurb-text { padding: 0 0 15px 0; }

    .buy-bundles-plans { margin:16px auto 0 auto; }

    .highlight-block { border:0; }

    .highlight-block ~ .homepage-pricing { border:1px solid #e4e4e4; border-left:0; }

    #hp-web-hosting .highlight-block { border: 1px solid #575ca6; border-bottom: 0 }

    #hp-reseller-hosting .highlight-block  { border: 1px solid #2aba9b; border-bottom: 0 }

    #hp-ssl-certificate .highlight-block  { border: 1px solid #f9935b; border-bottom: 0  }

    #hp-website-builder .highlight-block  { border: 1px solid #ca3051; border-bottom: 0 }

    .homepage-product-details span.price-per { color:#000; }

    .homepage-products { margin: 29px auto -37px auto; }

    .homepage-product-details span.product-tagline { padding: 1px 0 20px 0; }

    .homepage-product-details h2 {  margin: 16px 0 0 0;  padding: 0 0 0 0;  }

    .homepage-pricing {  padding: 28px 3%;  }

    .white-block { height: 163px; }

    .domain-hp-free-addons ul {  max-width: none;  width: 87.5%;  }

    .domain-hp-free-addons ul li { margin: 0 0 20px 0 !important }

    .domain-hp-content .lowest-price-tlds-list li
    { width: 49%; }

    .domain-hp-content .lowest-price-tlds-list li:nth-child(3) { width: 49.8% }

    .new-homepage-wrapper-dark-banner .bundles-hp-blurb-products { margin: 15px 0 0 0; }

    .homepage-content { margin-bottom: -17px; }

    .homepage-banner span.subtext,
    .homepage-content span.subtext,
    .hosting-homepage-banner-content .subtext,
    .hosting-homepage-banner-content ul li,
    .domain-hp-content .subtext, .domain-hp-free-addons .subtext
    { font-size: 13px; }

    .hosting-homepage-container .search-domain-bar { margin-top: -50px }

    .hosting-homepage-container .subtext { margin-bottom: -6px; }

}


/* Tablet View (above 768px and below 1000px) */

@media screen and (min-width: 768px) and (max-width: 1000px)
{

    /* Common */

    .homepage-product-details, .homepage-products div.hp-no-margin { width: 45%; display: inline-block; }

    .homepage-banner h1 { font-size: 26px; }

    .domains-homepage-container span.subtext { font-size: 15px; }

    .domain-hp-content a.more-tlds { font-size: 14px; }

    .homepage-pricing { padding: 35.5px 3%; }

    .homepage-banner form
    { margin: 12px auto 0 auto; }

    .homepage-banner .green-submit-button, .domains-homepage-container form .green-submit-button
    { font-size: 18px; font-weight: 400; border: 1px solid #d8d8d8; border-left: 0;
      border-radius: 0 4px 4px 0; -moz-border-radius:  0 4px 4px 0; -webkit-border-radius:  0 4px 4px 0;
      height: 56px; width: 102px; padding: 0; text-align: center; line-height: 54px; margin: 0 0 0 -10px;
    }

    .homepage-banner input[type="text"], .domains-homepage-container form input[type="text"]
    {  font-size: 18px;  height: 54px;  line-height: 54px;  padding: 0 0 0 16px;  width: 410px; font-weight: 400;  }

    .homepage-products { margin: 30px auto 0 auto; }

    .homepage-product-details h2 { padding: 17px 0 0 0; }

    .homepage-product-details span.product-tagline{ padding: 0 0 27px 0; }

    .homepage-pricing { padding: 29.8px 3%;  }

    .homepage-product-details span.price-per { color:#000; }


    /* Domain & Hosting Focused */

    .homepage-banner-lhs h1 { padding: 23px 0 7px 0; }

    .bundles-hp-blurb-text
    { border:1px solid #eee; border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px; padding: 0; }

    .bundles-hp-blurb-text ul { width: 70%; text-align: left;  }

    .bundles-hp-blurb { margin-left: -47.4%;  width: 94.8%; top:160px; }

    .bundles-hp-blurb h1 { font-size: 20px; }

    .buy-bundles-plans { width: 28%; display: inline-block; }

    .bundles-hp-blurb-text, .buy-bundles-plans { width: 100%; }

    .bundles-hp-blurb-text ul { width: 68%; }

    .buy-bundles-plans { display: inline-block; margin: 0 11px 0 0; width: 212px; line-height:38px; height: 38px; padding: 0; }

    .bundles-hp-blurb-products li { padding: 15px 0 11px 0; }

    .bundles-hp-blurb-products li .plus-icon
    { font-size: 0; background:url('/getImage.php?src=/bundle-plan-plus-tab.png') no-repeat; }

    .new-homepage-wrapper.add-top-space .banner-shadow, .new-homepage-wrapper-dark-banner.add-top-space .banner-shadow
    { top: 450px; }

    .new-homepage-wrapper.add-top-space, .new-homepage-wrapper-dark-banner.add-top-space
    {  background-position: center -3px;  min-height: 380px;  }

    .homepage-content h1 { padding: 58px 0 3px 0; }

    .new-homepage-wrapper-dark-banner.add-top-space .banner-shadow { top: 437px; }

    .new-homepage-wrapper-dark-banner .homepage-content h1 { padding: 45px 0 0 0; }

    /* Hosting Focused */

    .hosting-homepage-banner
    { background: url("/getImage.php?src=/home-hosting-tablet-bg.png") repeat-x center top; padding: 45px 0 0 0;  }

    .hosting-homepage-banner-content h1
    { font-size: 26px; line-height: 21px; padding: 0 0 10px 0; }

    .hosting-homepage-container .homepage-content h1 { font-size: 26px; }

    .hosting-homepage-banner-content .subtext { font-size: 15px; }

    .hosting-homepage-banner-content ul { display: inline-block; width: 745px; }

    .hosting-homepage-banner-content ul li { margin: 0 26px 5px 0; }

    .hosting-homepage-banner .web-hosting-supports, .hosting-homepage-dark-banner .web-hosting-supports
    { background-size: 90% 83%; }

    .search-domain-bar form { width: 525px; }

    .hosting-plans-price .homepage-pricing
    { position: absolute; width: auto; text-align: left; top:-10px; left: 80px; }

    .homepage-product-details span.price-value, .hosting-plans-price .homepage-pricing span.price-value {
        color: #2fbe15;  font-family: 'Open Sans', sans-serif;  font-size: 40px;  font-weight: bold;
        line-height: 44px;  margin: 0 0 -4px 3px;
    }

    .hosting-plans-price .homepage-pricing span.start-at {
        font-family: 'Open Sans', sans-serif; font-weight: 400;
        margin-bottom: 5px;
    }

    .hosting-plans-price .homepage-pricing span.price-symbol { margin: -10px 0 0 0 !important; }

    .hosting-homepage-banner .hosting-plan-icon, .hosting-homepage-dark-banner .hosting-plan-icon
    { background: url("/getImage.php?src=/hosting-homepage-plans-tablet.png") no-repeat; height: 221px; width: 585px; }

    .hosting-plans-price .homepage-pricing span.price-symbol { display: inline-block; }

    .hosting-plans-price .homepage-pricing span.price-per
    { font-family: 'Open Sans', sans-serif; font-weight: normal; font-size: 15px; display: inline-block; margin: 9px 0 0 0 !important; }

    .hosting-plans-price .homepage-pricing span.price-value
    { font-size: 38px; }

    .web-hosting-supports { display: none; }

    .hosting-homepage-banner
    { border-bottom: 1px solid #dfdfdf; box-shadow:4px 4px 6px #ececec; }

    .hosting-homepage-banner .hosting-plan-icon, .hosting-homepage-dark-banner .hosting-plan-icon
    { margin: 37px auto 0 auto; }

    .hosting-homepage-container  .homepage-content h1 { padding: 23px 0 3px 0; }

    .hosting-homepage-container .homepage-products { margin: 35px auto 0 auto; }

    .hosting-homepage-container .homepage-product-details span.price-value,
    .homepage-product-details span.price-value
    { line-height: 22px; }

    .homepage-product-details span.price-value,
    .homepage-product-details span.price-symbol,
    .hosting-homepage-container .homepage-product-details span.price-value,
    .hosting-homepage-container .homepage-product-details span.price-symbol
    { font-family: 'Open Sans', sans-serif; font-size: 22px; font-weight: bold; color:#000; }

    .homepage-product-details span.price-symbol,
    .hosting-homepage-container .homepage-product-details span.price-symbol
    { margin: -3px 0 0 0; }

    .hosting-homepage-container .homepage-product-details .green-submit-button
    { font-size: 15px; }

    .hosting-homepage-container .homepage-pricing { min-height: 105px; }

    .hosting-homepage-container .search-domain-bar input[type="text"],
    .hosting-homepage-container .search-domain-bar .green-submit-button
    { font-size: 18px; }

    .white-block { height: 170px; }

    .homepage-product-details span.start-at { display: block; text-align: right; margin: 0 0 3px 0; }


    /* Domain Focused */

    .domain-focused-homepage .homepage-banner .green-submit-button,
    .domains-homepage-container form input[type="text"]
    { font-size: 18px; }

    .domain-hp-free-addons ul li { font-size: 16px; font-weight: 400; margin: 0 0 20px 0 !important; width: 33%; }

    .domain-hp-free-addons ul li { display: inline-block; }

    .domain-hp-free-addons ul li:nth-child(4) { clear:left; }

    .domain-hp-free-addons ul { max-width: none; text-align: left; width: 94%; }

    .domain-hp-content .lowest-price-tlds-list { max-width:none; width: 94%; }

    .domain-hp-content .lowest-price-tlds-list li, .domain-hp-content .lowest-price-tlds-list li:nth-child(3) { width: 24%; }

    .domain-hp-content .lowest-price-tlds-list li:nth-child(1)
    { border-width: 0 1px 0 0; }

    .domain-hp-content .lowest-price-tlds-list li:nth-child(2) { border-width: 0 1px 0 0 }

    .domain-hp-content a.more-tlds, .domain-hp-content a.more-tlds:hover
    { margin:0; float: none; position: absolute; right: 2.6%; bottom: -26px; }

    .homepage-banner form { width: 526px; }

    .domain-hp-content .lowest-price-tlds-list li span.tld-name
    { color:#000; font-family: 'Open Sans', sans-serif; font-size: 18px; font-weight: bold; }

    .domain-hp-content .lowest-price-tlds-list li span.discounted-price
    { font-size: 16px; }

    .domain-hp-free-addons h1, .domain-hp-content h1 { font-size: 22px; }

    .domain-hp-content h1{ padding: 50px 0 30px 0; }

    .homepage-product-details .product-icon { height: 93px; }

    .homepage-content { margin-bottom: -20px; }

    .homepage-content h1 { font-size: 26px; margin-bottom: 4px; }

}

/* Common for Mobile & Tablet */
@media screen and (min-width: 320px) and (max-width: 1000px){

    .bundles-banner-content,
    .bundles-page-content,
    .bundles-plans
    {  margin: 0 auto; display: block;  width: 100%;  }

    .bundles-plans .bundles-plan-heading
    { border-left: 2px solid #9bcb44; border-radius: 2px 2px 0 2px; -moz-border-radius:  2px 2px 0 2px; -webkit-border-radius:  2px 2px 0 2px; }

    .bundles-plans .bundles-price span.bundle-currency .WebRupee { font-weight: bold; }

    .bundles-plans-two-cols, .bundles-plans-three-cols
    { border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; }

    .search-form-container .validation-error-message
    { position: relative;  bottom: 0;  margin: 5px 0; }

}

/* Mobile View (above 320px and below 768px) */

@media screen and (min-width: 320px) and (max-width: 767px)
{

    .bundles-blocks.col1 p,
    .bundles-blocks.col3 p,
    .bundles-blocks.col2 ul li,
    .bundles-blocks.col3 ul li,
    .bundles-banner-content span.sub-text,
    .bundles-plans .bundles-plans-arrow,
    .bundles-page-content span.sub-text
    { display: none; }



    /* Plans */

    /* Select Bundle */

    .bundles-page-content h1 { font-size: 20px; margin:26px auto -4px auto; }

    .bundles-plans-two-cols, .bundles-plans-three-cols
    { margin:30px auto 0 auto; width: 87.5%; }

    .bundles-plans div.bundles-blocks,
    .bundles-plans .plus-icon
    { display: block; }

    .bundles-blocks.col1,
    .bundles-blocks.col2,
    .bundles-blocks.col3,
    .bundles-plans-three-cols .bundles-blocks.col1,
    .bundles-plans-three-cols .bundles-blocks.col2,
    .bundles-plans-three-cols .bundles-blocks.col3,
    .bundles-plans-two-cols .plus-icon,
    .bundles-plans .plus-icon
    { padding: 0 4.7%; width: 90.6%; }


    .bundles-plans .bundles-plan-heading h2,
    .bundles-plans .bundles-step-one h2,
    .bundles-plans .bundles-step-two h2,
    .bundles-plans .bundles-step-three h2,
    .bundles-plans .bundles-step-four h2
    { font-size: 16px; line-height: 21px; }

    .bundles-plans .bundles-plan-heading span.plans-sub-text
    { font-size: 13px; margin: 0 0 11px 0; padding: 0 0 0 4.7%; }

    .bundles-plans .bundles-plan-heading h2,
    .bundles-plans .bundles-step-one h2,
    .bundles-plans .bundles-step-two h2,
    .bundles-plans .bundles-step-three h2,
    .bundles-plans .bundles-step-four h2
    { padding: 12px 0 0 4.7%; }

    .bundles-plans .plus-icon { height: auto; }

    .bundles-plans .bundles-blocks h3,
    .bundles-dsearch-result h3
    { font-size: 14px; margin: 12px 0 9px 0; }

    .bundles-plans h3 span.bundles-product-subt { font-size: 12px; top: 20px; }

    .bundles-page-note { font-size: 12px; line-height: 16px; margin: 8px auto 0 auto; width: 87.5%; }

    .bundles-page-note a { display: block; }

    .bundles-plans .vertical-sep
    { background: #e7e7e7; height: 1px; top:15px; width: 90.6%;  z-index: 1000; }

    .bundles-plans .bundles-plans-two-cols .vertical-sep,
    .bundles-plans .bundles-plans-three-cols .vertical-sep
    { left:4.7%;   }

    .bundles-plans .plus-icon .plus-sign
    { height: 20px; margin: 0 auto -2px auto; padding: 0 6px; width: 20px; z-index: 1001; }

    .bundles-plans .plus-icon { font-size: 40px; }

    .bundles-plans .bundles-plans-two-cols .bundles-price,
    .bundles-plans .bundles-plans-three-cols .bundles-price
    { width: 90.6%; margin: 20px auto 15px auto; padding: 5px 0 0 0; border-top:1px solid #e7e7e7; text-align: left; }

    .bundles-plans .bundles-price span, .bundles-plans .bundles-price input
    { display: inline-block !important; }

    .bundles-plans .bundles-price .bundle-price-value { padding: 20px 0 0 0; font-size: 22px; }

    .bundles-plans .bundles-price .bundle-currency,
    .bundles-plans .bundles-price .bundle-validity
    { font-size: 12px; }


    .bundles-plans .bundles-price .bundle-price-value,
    .bundles-plans .bundles-price .bundle-validity
    { vertical-align: bottom; }

    .bundles-plans .bundles-price .bundle-validity
    { margin: 0 0 5px 0; }

    .bundles-plans div.bundles-price{ display: block; position: relative; }

    .bundles-plans .bundles-price .bundles-green-button
    { position: absolute; right: 0; padding: 0; font-size: 15px; width: 112px; height: 36px; line-height: 34px;
      border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px;
    }

    .bundles-plans .bundles-price .price-note
    { display: block !important; margin: 0; text-align: right; }

    .bundles-plans .bundles-price .bundle-currency { margin: 24px 0 0 0; }


    /* Step - 1 */

    .bundles-plans .bundles-step-one .search-form-container
    { margin: 20px auto 10px auto; width: 97.5%; padding: 0.6%; text-align: center;  }

    .bundles-plans .bundles-step-one .search-form-container input[type="text"]
    {  height: auto; line-height: normal; padding: 13.5px 0 13.5px 12px; width: 61.5%; }

    .bundles-plans .bundles-step-one .search-form-container .bundles-green-button
    {  height: auto; line-height: normal; padding: 13px 0; width: 31.5%; font-size: 15px;
       border-radius: 0 2px 2px 0; -moz-border-radius: 0 2px 2px 0; -webkit-border-radius: 0 2px 2px 0;
    }

    .bundles-step-one, .bundles-step-two, .bundles-step-three, .bundles-step-four
    { border-radius: 0; -moz-border-radius: 0; -webkit-border-radius: 0;
      margin: -3px auto 0 auto; padding: 20px 4.5%; position: relative; width: 78.5%;
    }

    .search-form-container { position: static; }

    .bundles-searchform-uparrow { top:-13px; left: auto; right: 25%; }

    .bundles-more-plans, .bundles-back-link, .change-choosen-domain { font-size: 12px; bottom: -20px; }

    .bundles-more-plans { right: 6.25%; }

    .bundles-back-link { left: 6.25%; }

    .bundles-package-selected.step-one
    { padding-bottom: 20px; }


    .bundles-green-button.bundles-selected-button
    {  background: url(/getImage.php?src=images/bundles-selected-button.png) no-repeat #2fbe15 16px center;
       text-align: right;  padding: 0 19px 0 0 !important;
    }

    .search-form-container .validation-error-message
    { font-size: 11px; line-height: 14px; position: relative;  bottom: 0;  margin: 5px 0; }

    /* Step - 2 */

    .dsearch-wrapper
    { width: 87.5%; margin: 20px auto 0 auto; padding: 5px 4% 5px 4%; border-radius: 2px; -moz-border-radius: 2px; -webkit-border-radius: 2px; }

    .domainname-value, .domain-status-message { font-size: 14px; line-height: 18px; }

    .domain-found-icon, .domain-notfound-icon
    { height: 16px; margin: 5px 2px 0 0; width: 16px; }

    .domain-found-icon
    { background: url('/getImage.php?src=bundles-domain-avail-mobile.png') no-repeat; }

    .domain-notfound-icon
    { background: url('/getImage.php?src=bundles-domain-not-avail-mobile.png') no-repeat; }

    .dsearch-wrapper .bundles-green-button
    { margin: 3px 0 0 0; }

    .bundles-choosen-domain { padding: 7px 10px; width: auto; }

    .change-choosen-domain {  bottom: -20px;  right: 4.7%;  left: auto; z-index: 99;  }

    .dsearch-wrapper .bundles-green-button
    {
        border-radius: 26px;  -moz-border-radius: 26px;  -webkit-border-radius: 26px;
        font-size: 14px;  height: 28px;  line-height: 26px;  padding: 0;  text-align: center;  width: 64px;
    }


    /* Step - 3 */

    .bundles-plans .bundles-step-four .bundles-green-button
    {  font-size: 15px; margin: 6px auto 0 auto; width: 100%; padding: 8.5px 0;  }

    /* Show/Hide Bundle Features */

    .bundles-plan-heading
    { position: relative; }

    .toggle-bundle-details
    { position: absolute; right: 4.7%; background:url('/getImage.php?src=show-hide-bundle-contents.png') no-repeat 0 0;
      height: 11px; top:25px; cursor: pointer; width: 20px;
    }

    .toggle-bundle-details.up-arrow
    { background-position: -35px 0; }


    .bundles-blocks.col2 ul li,
    .bundles-blocks.col3 ul li,
    .bundles-blocks.col2 ul li span,
    .bundles-blocks.col3 ul li span,
    .bundles-blocks.col1 p,
    .bundles-blocks.col3 p
    { padding: 0; font-size: 12px; line-height: normal;  }


    .bundles-blocks.col2 ul li span,
    .bundles-blocks.col3 ul li span
    { margin:  0 0 0 -4px; }

    .bundles-blocks.col2 ul li,
    .bundles-blocks.col3 ul li,
    .bundles-blocks.col1 p,
    .bundles-blocks.col3 p { padding: 0 0 3px 0; }


    .bundles-blocks.col1 p em { font-size: 11px; margin: 1px 0 0 0;  }

    .bundles-plans .bundles-step-one h2
    { padding: 0 0 0 4.7%; margin: -3px 0 -4px 0; }

    .bundles-plans .bundles-step-four h2
    { padding: 0 0 0 4.7%; margin: -3px 0 0 0; }

    /* Banner */

    .bundles-banner-wrapper
    { height: auto; background: url('/getImage.php?src=bundles-banner-bg-mobile.png') repeat-x center top #9bcb44; }

    .bundles-banner-content h1 { padding: 25px 0; font-size: 20px; line-height: 26px; }

    .bundles-banner-img {  background-size: 280px 113px; margin: 0 auto; height: 113px; width: 280px;  }

    /* Small fixes */

    .dsearch-wrapper .lfloat,
    .dsearch-wrapper .rfloat
    { display: inline-block; vertical-align: top; float: none; }

    .dsearch-wrapper .lfloat { width: 57%; }

    .dsearch-wrapper .lfloat .domainname-value { word-wrap: break-word; width: 75%; }

    .dsearch-wrapper .rfloat { width: 41%; text-align: right; }



}


/* Mobile & High Resolution Display @2x */

@media
only screen and (-webkit-min-device-pixel-ratio: 2),
only screen and (   min--moz-device-pixel-ratio: 2),
only screen and (     -o-min-device-pixel-ratio: 2/1),
only screen and (        min-device-pixel-ratio: 2),
only screen and (                min-resolution: 192dpi),
only screen and (                min-resolution: 2dppx)  and (min-width: 320px) and (max-width: 767px)
{
    .bundles-banner-wrapper
    { height: auto; background: url('/getImage.php?src=bundles-banner-bg-mobile.png') repeat-x center top #9bcb44; }

    .bundles-blocks ul { width: auto; }
}



/* Tablet View (above 768px and below 1000px) */

@media screen and (min-width: 768px) and (max-width: 1000px) {


    .bundles-blocks.col1,
    .bundles-blocks.col2,
    .bundles-blocks.col3,
    .bundles-plans-three-cols .bundles-blocks.col1,
    .bundles-plans-three-cols .bundles-blocks.col2,
    .bundles-plans-three-cols .bundles-blocks.col3
    { width: auto; }

    .bundles-plans-three-cols .bundles-blocks.col1,
    .bundles-plans-three-cols .bundles-blocks.col2,
    .bundles-plans-three-cols .bundles-blocks.col3
    { max-width: 205px; }

    .bundles-plans .bundles-plans-arrow { display: none; }

    .bundles-plans-two-cols, .bundles-plans-three-cols
    { margin: 30px auto 0 auto; width: 95%; }

    .bundles-plans .bundles-plans-two-cols .bundles-price,
    .bundles-plans .bundles-plans-three-cols .bundles-price
    { display: block; width: 96%; border-top:1px solid #e9e9e9; padding: 0; margin: 0 2%; }

    .bundles-blocks ul { margin:0 0 0 58px; width: auto; }

    .bundles-page-note { margin: 10px auto; width: 95%; }


    /* Banner */

    .bundles-banner-content h1 { font-size: 26px; line-height: 21px; padding: 50px 0 0 0; }

    .bundles-banner-content span.sub-text
    { font-size: 15px; line-height: 21px; }

    .bundles-banner-img { margin: 28px auto 0 auto; background-size: 585px 232px; height: 232px; width: 585px; }

    /* Page content */

    .bundles-page-content h1
    { font-size: 22px; margin: 46px 0 0 0; }

    .bundles-page-content span.sub-text
    { margin: 10px 0 -3px 0; }


    /* Select Bundle */

    .bundles-plans .bundles-plan-heading h2,
    .bundles-plans .bundles-step-one h2,
    .bundles-plans .bundles-step-two h2,
    .bundles-plans .bundles-step-three h2,
    .bundles-plans .bundles-step-four h2
    { font-size: 18px; padding: 15px 0 0 2.5%; }

    .bundles-plans .bundles-plan-heading span.plans-sub-text
    { font-size: 14px; padding: 0 0 0 12px }

    .bundles-blocks.col1 { padding: 15px 0 0 15px; }

    .bundles-plans-three-cols .bundles-blocks.col2,
    .bundles-blocks.col3
    { padding: 15px 0 0 0; }

    .bundle-currency,
    .bundle-price-value,
    .bundle-validity
    { float: left; }

    .bundles-plans .bundles-price .bundles-green-button { margin: 15px 0 0 0; }

    .bundles-plans .bundles-price .price-note { margin: 5px 6px 14px 0; }

    .bundles-plans .bundles-price .bundle-price-value { font-size: 34px; margin: 22px 0 0 0; }

    .bundles-plans .bundles-price .bundle-currency,
    .bundles-plans .bundles-price .bundle-validity
    { font-size: 14px; }

    .bundles-plans .bundles-price .bundle-validity {  margin: 39px 0 0 0;  }

    .bundles-plans .bundles-price .bundle-currency {  margin: 27px 3px 0 0;  }


    /* Step - 1 */

    .bundles-step-one
    { width: 95%; padding: 0; margin: -2px auto 0 auto; border-radius: 0; position: relative; }

    .bundles-plans .bundles-step-one .search-form-container
    { text-align: center;  width: 95%; position: static;  margin: 20px auto; }

    .bundles-plans .bundles-step-one .search-form-container input[type="text"]
    { height: 40px; line-height: 40px; width: 75%; }

    .bundles-plans .bundles-step-one .search-form-container .bundles-green-button
    { height: 42px; line-height: 40px; width: 21%; }

    .bundles-searchform-uparrow { left: auto; right: 10.5%; top:-13px; }

    .bundles-more-plans, .bundles-back-link, .change-choosen-domain
    { right: 2.5%; bottom: -30px; }

    .bundles-back-link
    { left: 2.5%; right: auto; }

    .bundles-page-content .step-one h1 { margin: 46px 0; }

    .search-form-container .validation-error-message { text-align: left; margin: 5px auto; width: 99%; }


    /* Step - 2 */

    .dsearch-wrapper { margin: 0 auto 20px auto; padding: 18px 2.5% 15px 2.5%; width: 90%; }

    .domain-found-icon, .domain-notfound-icon
    { width: 30px; height: 30px; }

    .domain-found-icon 
    { background: url('/getImage.php?src=bundle-domain-found-tab.png') no-repeat; }
    
    .domain-notfound-icon
    { background: url('/getImage.php?src=bundle-domain-notfound-tab.png') no-repeat; }

    .domainname-value, .domain-status-message { font-size: 16px; margin: -5px 0 0 -4px; }

    .dsearch-wrapper .bundles-green-button { margin: 0; }

    /* Step - 4 */

    .bundles-plans .bundles-step-four
    { width: 95%; padding: 0 0 20px 0; margin: -2px auto 0px auto; border-radius: 0; }

    .bundles-plans .bundles-step-four .bundles-green-button
    { margin: -12px 0 0 0; }

    .bundles-dsearch-result #preLoader { margin: 0 auto 20px 25% !important; }

    .bundles-choosen-domain { margin: 8px 0 28px 40px;  }

    .change-choosen-domain { right:0; bottom: 12px; left: auto; }

    .bundles-package-selected.step-four {  min-height: 40px; }

    .bundles-package-selected.step-four .bundles-back-link,
    .bundles-package-selected.step-four .bundles-more-plans
    { bottom: -20px }

}/* Common for Mobile & Tablet */

@media screen and (min-width: 320px) and (max-width: 1000px)
{
    .chfaqrow .faqicon
    { background: url('/getImage.php?src=ch-faq-toggle-mobile.png') no-repeat;
        height: 20px; width: 20px;  margin: 0; position: absolute; right: 0; top:12px; }

    .chfaqrow.scactive .faqicon
    { background-position: 0 -9px; height: 1px; top:20px; }

    .chfaqtitle
    { position: relative; }

    .cloud-plans-container span.prev-sep,
    .cloud-plans-container span.next-sep,
    .cloud-features-wrapper span.prev-sep,
    .cloud-features-wrapper span.next-sep
    { display: none; }

    .cloud-plans-container .server_loc_tabs{
        margin: 60px auto 0 auto;
    }
}

/* Mobile View (above 320px and below 768px) */

@media screen and (min-width: 320px) and (max-width: 767px)
{
    /* Common */

    .chcloudwrap,
    .cloud-features-wrapper,
    .cloud-banner-container,
    .cloud-feature-container,
    .cloud-banner-meta-container,
    .cloud-plans-container,
    .chhero
    { width: 100%; }

    .lmargin { margin: 0; }


    /* Banner */

    .chcloudwrap
    { background: url('/getImage.php?src=ch-banner-bg-mobile.png') repeat-x center top #40c2ff; position: relative; }

    .chcontent1, .chcontent2
    { font: bold 20px/26px 'Open Sans', sans-serif; }

    .chcontent1, .chcontent2, .chftlist
    {  display: block; text-align: center; float: none; padding: 0; margin: 0 auto;  width: 320px; }

    .chcontent1 { padding: 26px 0 0 0;  }

    .chftlist { margin: 10px auto 0 auto; }

    .chfeatures { width: auto; float: none; margin: 0 auto; padding: 0; }

    .chfticon1 { background: url('/getImage.php?src=ch-banner-icons-mobile.png') no-repeat left 92px top; margin: 0 auto -15px auto;  }
    
    .chfticon2 { background: url('/getImage.php?src=ch-banner-icons2-mobile.png') no-repeat left 52px top;  }

    .chfeatures
    { font: 600 16px/26px 'Open Sans', sans-serif; }

    .chhero
    { background: url('/getImage.php?src=ch-banner-hero-mobile.png') no-repeat center bottom 90px !important; min-height: 422px; }

    .chcontent3, .chcontent4
    { position: absolute; background: #222; bottom:42px; display: block; width: 100%; text-align: center;  }

    .chcontent3 {  padding: 5px 0; }

    .chcontent3 .starting-at { font: 600 20px 'Open Sans', sans-serif;  }

    .chcontent3 span.inline-block.vbottom.price-value
    { font: 600 23px 'Open Sans', sans-serif; vertical-align: middle; margin: -3px 0 0 0; position: static; }

    .chcontent3 span.inline-block.vbottom.valid-till {  margin: 0 0 7px -8px;  }

    .chcontent3 span.inline-block.vtop.currency-sym
    { margin: 7px -2px 0 0  }

    .chcontent3 .valid-till,
    .chcontent3 .currency-sym
    { font: 400 14px 'Open Sans', sans-serif; text-transform: lowercase; }

    .chcontent4 { bottom:0; text-align: center; padding: 0 0 15px 0; }

    .chcontent4 .green-button-style
    {
        font: 400 15px 'Open Sans', sans-serif; height: auto; width: 110px; padding: 7.5px 0; margin: 0 auto;
    }

    /* Tabs */

    .cloud-tabs-txt
    {  height: 35px; background: #f8f8f8; border-bottom: 1px solid #f0f0f0;  width: 100%;  position: absolute; bottom:-35px; z-index: 99; }

    .cloud-tabs-txt span
    {  font: 400 13px/22px 'Open Sans', sans-serif; color: #000; text-transform: uppercase; line-height: 35px; display: inline-block; }

    .tabs-txt-content { width: 100%; margin: 0 auto; text-align: center; }

    .cloud-tabs-txt span { padding: 0 25px; }

    .cloud-tabs-txt span:first-child { float: left; }

    .cloud-tabs-txt span:last-child { float: right; text-align: right; }

    .cloud-tabs-txt span.clicked { border-bottom: 2px solid #3fbffb; }

    /* Plans */

    .cloud-plans-container h2
    { margin: 0 auto -10px auto; padding: 0; }

    .cloud-plans-container h2 span.heading-text
    { font-family: 'Open Sans', sans-serif; font-size: 20px;font-weight: bold; }

    .cloud-plan, .plan-featured.cloud-plan
    { width: 87.5%; margin: 30px auto 0 auto;  }

    span.cloud-plan-name { font-size: 18px; padding: 9px 0; }

    span.cloud-plan-price { font-size: 24px; font-weight: 600; padding: 8px 0 6px 0; }

    .cloud-plan span.plan-valid { margin: 0 0 2px 0; }

    .cloud-plan span.price-sym {  margin: 4px -2px 0 0;  }

    .cloud-plan ul { margin: 16px auto 0 auto; }

    .cloud-plan ul li { font-size: 14px; padding: 0 0 12px 0; }

    .cloud-plan ul li.dropdown { margin: 5px 0 0 0; padding: 20px 0;  }

    .cloud-plan select
    { background: url('/getImage.php?src=sh-hosting-plans-dd.png') no-repeat #fff right center; width: 100%; border-radius: 0; padding: 3.5px 8px;  }

    .cloud-plan .green-button-style
    { font-size: 15px; width: 100%; border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px; height:36px; line-height: 36px; }


    /* Features */

    .cloud-feature-container { position: relative; }

    .cloud-feature-container span.subtitle-one, .cloud-feature-container span.subtitle-two
    { font: 400 18px 'Open Sans', sans-serif; color:#40c2ff; position: relative; top:-12px; background: #fff; display: block; text-align: center; width: 95%; margin: 0 auto; }

    .cloud-feature-container span.subtitle-two { text-transform: capitalize; }

    .cloud-feature-content.upperborder { border-top:1px solid #ddd; }

    .chsubstitle
    { display: none; }

    .cloud-features-wrapper .cloud-feature-icon.two-fast
    { top:48px; }

    .cloud-features-wrapper .cloud-feature-icon.initiate-dashboard
    { top:28px; }

    .cloud-feature-content
    { border-right:1px solid #ddd; border-left:1px solid #ddd; width: 87.5%; margin: 0 auto; padding: 0;
      display: block; position: relative; padding-bottom: 20px;
    }

    .cloud-feature-content.last { margin-right: auto; margin-top:37px; border-top:1px solid #ddd; }

    .cloud-feature-content.small-screen { border-bottom:1px solid #ddd; }

    .cloud-features-wrapper .cloud-feature-icon
    { background: url('/getImage.php?src=cloudsites-features-mobile.png') no-repeat;
        background-size: auto; width: 43px !important; margin: 0; position: absolute;
        left: 20px; top: 7px;
    }

    .cloud-features-wrapper .two-fast.cloud-feature-icon { background-position: 0 0; height: 47px; }

    .cloud-features-wrapper .infinite-scale.cloud-feature-icon { background-position: -48px 0; height: 40px; }

    .cloud-features-wrapper .simple-cp.cloud-feature-icon { background-position: -191px 0; height: 43px; }

    .cloud-features-wrapper .stable-rock.cloud-feature-icon { background-position: -95px 0; height: 44px; }

    .cloud-features-wrapper .auto-fail.cloud-feature-icon { background-position: -143px 0; height: 41px; }

    .cloud-features-wrapper .initiate-dashboard.cloud-feature-icon { background-position: -240px 0; height: 43px; }

    .cloud-features-wrapper h3 { font-size: 15px; }

    .cloud-features-wrapper p { font-size: 14px; }

    .cloud-features-wrapper h3, .cloud-features-wrapper p
    { display: block; vertical-align: top; text-align: left; width: auto; padding: 0 20px 0 80px; }

    .cloud-features-wrapper h3 { margin: 0 0 5px 0; }

    .cloud-features-wrapper h2 span.heading-text
    { font-family: 'Open Sans', sans-serif; font-size: 20px; line-height: 26px; margin: 20px auto 0 auto; width: 87.5%;font-weight: bold; }

    .cloud-feature-container .cloud-feature-content.bottomborder
    { border-bottom: 1px solid #ddd; }

    /* FAQ */

    .chfaqwrap
    { width: 87.5%; margin: 30px auto 0 auto;
      border: 0; border-bottom: 1px solid #ddd; border-top: 1px solid #ddd; border-radius: 0; -moz-border-radius: 0; -webkit-border-radius: 0;
    }

    .chdescalign
    { font: 400 14px/18px 'Open Sans', sans-serif; color: #000; padding: 0 0 13px 12px; margin: -10px 0 0 0;  background: #fff }

    .chfaqtitle { height: auto; }

    .chfaqhead, .chfaqrow.scactive .chfaqtitle
    { background: none; margin: 0; font: 400 14px/21px 'Open Sans', sans-serif; color: #000; }

    .chfaqhead { margin: 0 25px 0 0; }

    .chfaqtitle
    { padding: 13px 0; }

}


/* Tablet View (above 768px and below 1000px) */

@media screen and (min-width: 768px) and (max-width: 1000px)
{
    /* FAQ */

    .chfaqwrap { margin: -17px auto 0 auto; width: 95%; }

    .chfaqhead { font-size: 14px; }

    .chfaqdesc { font-size: 14px; font-weight: 400; }

    .chfaqtitle { height: auto; }

    .chfaqrow .faqicon, .chfaqrow.scactive .faqicon { right: 20px; }

    .chfaqrow .faqicon { top:18px; }

    .chfaqrow.scactive .faqicon { top:30px; }


    /* Plans */

    .cloud-plans-container { width: 100%; }

    .cloud-plan, .plan-featured.cloud-plan { margin: 0 8px 0 0; width: 25%; }

    .ms-pagecontents-wrapper .cloud-plans-container h2 { margin: -11px 0 2px 0; }

    .ms-pagecontents-wrapper .cloud-plans-container .cloud-plan { margin-bottom: -11px; }

    .cloud-plan ul li { font-size: 14px; }

    span.cloud-plan-name { font-size: 16px; }

    .cloud-plan select
    {
        -webkit-appearance: none;  -moz-appearance: none;
        display: inline-block;
        background: url(/getImage.php?src=images/selectbox-icon.png) no-repeat right #fff;
        border-radius: 0;  -moz-border-radius: 0;  -webkit-border-radius: 0;
        padding: 6px 22px 6px 10px; width: 100%;
    }

    .cloud-plan .green-button-style
    { font-size: 14px; height: auto; line-height: normal; margin: 7px 0 0 0; padding: 8px 0; width: 100%; }

    span.cloud-plan-price
    { font-size: 24px; }

    .cloud-plan span.plan-valid { font-size: 12px; margin: 0 0 5px -3px; }

    .cloud-plan span.price-sym { font-size: 14px; margin: 3px -3px 0 0  }

    /* Features */

    .cloud-feature-container { width: 95%; }

    .cloud-features-wrapper h2 span.heading-text { font: normal 22px 'Open Sans', sans-serif; font-weight: bold; }

    .cloud-feature-content { width: 28%; padding: 0 45px 0 0; margin: 0; }

    .cloud-feature-content.rborder { padding: 0 20px 0 0;  }

    .cloud-feature-content.lmargin { padding: 0 0 0 35px; }

    .cloud-feature-content.bpadding { padding-top: 35px; }

    .cloud-features-wrapper h3 { font-size: 16px; }

    .cloud-features-wrapper p { font-size: 14px; }

    .cloud-features-wrapper .cloud-feature-icon { margin: 0 0 12px 0; }

    .chsubt { font-size: 20px }

    .chprev-sep, .chnext-sep
    { margin: 0 5px 10px; width: 15px; }

    .chsubstitle {  margin-top: -22px; margin-bottom: 4px;  padding: 0 0 10px 0;  }

    /* Banner */

    .chcloudwrap {  background-position: right -23px bottom -17px;  background-size: 1000px 320px;  }

    .chhero
    { background-size: 768px 351px; background-position: right 20px top -1px; background-repeat: no-repeat;
      width: 100%; min-height: 350px;
    }

    .chcontent1 { font-size: 22px; font-family: 'Open Sans', sans-serif; padding-top: 50px; padding-left: 20px;font-weight: bold; }

    .chfticon1 { background-size: 15px 24px; background-color: transparent;  margin: 0 0 8px 0;  }

    .chcontent2 { font-size: 26px; font-family: 'Open Sans', sans-serif; padding-left: 20px;font-weight: bold;  }

    .chfticon2 {  background-size: 18px 18px; background-color: transparent; margin: 0 0 23px 0;  }

    .chfeatures { font-size: 20px; }

    .chcontent3 { font-size: 24px; padding-left: 20px; }

    .chcontent3 .price-value { font-size: 24px; font-weight: 400; }

    .chcontent3 .valid-till
    { font-weight: 400; font-size: 16px; text-transform: lowercase;  margin: 0 0 0 -7px;  vertical-align: middle; }

    .chcontent3 .currency-sym
    { font-weight: 400; font-size: 18px; margin: 0 -3px 0 0; }

    .chftlist {  margin: 23px 0 0 20px; }

    .chfeatures {  height: auto;  padding: 0 0 0 23px;  }

    .chcontent4 .green-button-style {  margin: 7px 0 0 20px;  }


}/* Mobile View (above 320px and below 768px) */

@media screen and (min-width: 320px) and (max-width: 767px) {

    .ee-lite-highlights,
    .ee-lite-plan-divider,
    .ee-upsell,
    .ee-lite-faqs
    { display: none !important; }

    #ee-lite-wrapper, .ee-lite-banner, .ee-lite-banner-content, .ee-lite-plans, .ee-lite-features, .ee-lite-faqs
    { width: 100%; }

    .ee-lite-banner p img
    { height: 125px; width: 87.5%; }


    /* Banner CSS */

    .ee-lite-banner { padding: 25px 0 0 0; }

    .ee-lite-banner h1.ee-lite-description
    { font-family: 'Open Sans', sans-serif !important; font-size: 20px; font-weight: bold;
      line-height: 27px; margin: 0 auto; width: 235px;
    }

    .ee-lite-banner-content { position: relative;  }

    .ee-lite-banner-content p
    { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400;
      line-height: 20px; margin: 7px auto 0 auto; width: 275px;
    }

    .ee-lite-banner-content div.rfloat
    {    float:none; position: absolute; left: 50%; margin-left:-60px; top:-220px;  height: 35px; width: 120px; }

    .ee-lite-banner-content div.rfloat p
    { margin:0; width: 120px; }

    .ee-lite-banner-content div.rfloat p a
    {
        background: url('/getImage.php?src=/eelite-preview-mobile.png') no-repeat 28px 11px;
        border:1px solid #000; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px;
        font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400; color:#000;
        margin: 37px 0 0 0; padding: 0 0 0 20px; line-height: 30px; height: 30px; text-align: center; width: 100px;
    }


    .ee-lite-banner-content p img { margin: 80px 0 -6px 0; }

    .ee-lite-banner-links { margin: 0; }

    .ee-lite-banner-links .lfloat { height: auto; margin: 0; width: 100%; }

    .ee-lite-banner-links .lfloat p span { font-size: 18px; font-weight: 600; }

    .ee-lite-banner-links p .white-text
    { font-size: 16px !important; font-weight: 400 !important; }

    .ee-lite-banner-links .lfloat p
    { font-size: 14px !important; font-weight: 400 !important; margin: 0; padding: 6px 0; text-align: center; width: 100%; }

    .ee-lite-banner-links .lfloat p sup { vertical-align: top; }

    @-moz-document url-prefix() {
        .ee-lite-banner-links .lfloat p sup { vertical-align: text-top; }
    }

    .ee-lite-banner-img{ height: 121px; width:275px }


    /* Plans CSS */

    .ee-lite-plans { margin-top: 25px; }

    .ee-lite-plans h2, .ee-lite-features h2
    { font-family: 'Open Sans', sans-serif !important; font-size: 20px; font-weight: bold;
      line-height: 26px; color:#000; width: 87.5%; margin: 0 auto 25px auto;
    }

    .ee-lite-plans-form
    { border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; margin: 0 auto; width: 87.5%; text-align: center;  }

    .ee-lite-input-wrapper, .ee-lite-select-wrapper, .ee-lite-total-wrapper, .ee-lite-submit-wrapper, .ee-lite-total-wrapper .total-value
    { background: #fff; display: block; padding: 20px 0 0 0; margin: 0 auto; width: 85%;
      border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; text-align: left;
    }

    .ee-lite-select-wrapper { padding: 8px 0 0 0; }

    .ee-lite-submit-wrapper { width: 85% !important; text-align: center; }

    .ee-lite-total-wrapper .total-value{ width: 100%; margin: 6px auto 0 auto; }

    .ee-lite-input-wrapper label, .ee-lite-select-wrapper label, .ee-lite-total-wrapper label
    { font-family: 'Open Sans', sans-serif; font-size: 12px; font-weight: 400; color:#000; }

    .ee-lite-total-wrapper .total-value
    { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 600; color:#1b1b1b; }

    .ee-lite-total-wrapper .total-value .currency_symbol { margin: 0 0 0 10px; }

    .ee-lite-input-wrapper input, .ee-lite-submit-wrapper #purchase_button
    { margin: 0; width: 100%; }

    .ee-lite-submit-wrapper #purchase_button
    { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400; color:#fff; margin: 0 0 -2px 0;  }

    .ee-lite-total-wrapper .total-value { background: #f0f0f0; padding: 0; }

    .ee-lite-input-wrapper input.frm-field { margin: 6px 0 0 0; padding: 6px 0 6px 4%; text-align: left; width: 96%; }

    .eelite-per-account-space
    { color:#434343; font-family: 'Open Sans', sans-serif !important; font-size: 10px !important; font-weight: 400;  }

    .frm-select#ee-value, .ee-lite-input-wrapper input.frm-field
    { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; color:#1b1b1b; border:1px solid #ddd; box-shadow: none;  }

    .frm-select#ee-value, .ee-lite-input-wrapper input.frm-field, .ee-lite-total-wrapper .total-value
    {  border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; }

    .ee-lite-plan-details { margin: 10px auto 0 auto; }

    .ee-lite-plan-details{ line-height: 17px; width: auto; }


    /* Select Domain Modal */

    #select-domain-modal h1.ms-modal-title
    { font-size: 18px; padding: 0 0 12px 0; }

    #select-domain-modal { width: 87.5%; margin-left:-43.75%; z-index: 10000 !important }

    #select-domain-modal .use-existing input[type="text"],
    #select-domain-modal .register-new input[type="text"]
    { width: 205px; }

    #select-domain-modal p label.frm-label
    { font-size: 13px; margin: 0; }

    #select-domain-modal .continue-button-container input#continue_action
    { font-size: 15px !important; padding: 0 42px; }

    #select-domain-modal .inner-content p.purchase-security label
    { font-size: 14px; font-weight: 400; width: 75%; }

    #select-domain-modal .inner-content p
    { font-size: 13px; }

    #select-domain-modal .gray-bgcolor .inner-content p
    { width: 80%; }

    #select-domain-modal .gray-bgcolor .inner-content h2
    { font-size: 14px;   }

    .purchase-codeguard
    { margin-top:20px; }


    /* Features CSS */

    .ee-lite-features { margin-top: 45px; }

    .ee-lite-features ul { margin:0 auto; width: 87.5%; }

    .ee-lite-features ul li, .ee-lite-features ul li.no-margin
    { border: 1px solid #ddd; margin: 0 0 -1px 0 !important; padding: 10px 0; text-align: left; position: relative; }

    .ee-lite-features ul li h3
    {   display:inline-block; *display:inline; zoom: 1;
        font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400;
        margin:0 0 0 8px; line-height: 21px; text-align: left; vertical-align: middle;
    }

    .ee-lite-features ul li h3, .ee-lite-features ul li h3.small-heading
    { width: 68%; }

    .ee-lite-features ul li p
    {   border-top:1px solid #ddd; font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400;
        line-height: 18px; margin: 10px 0 0 0; padding: 10px 12px 0 12px; text-align: left; display: none;
    }

    #ee-lite-design,
    #ee-lite-spam,
    #ee-lite-backup,
    #ee-lite-security,
    #ee-lite-contacts,
    #ee-lite-social
    { background: url('/getImage.php?src=/eelite-features-mobile.png') no-repeat; vertical-align: middle; margin: 0 0 0 11px; }

    #ee-lite-design
    { width:30px; height:29px; background-position:0 0; }

    #ee-lite-spam
    { width:30px; height:29px; background-position:0 -34px; }

    #ee-lite-backup
    { width:30px; height:29px; background-position:0 -69px; }

    #ee-lite-storage
    { background-size: 33px 32px; height: 32px; width: 33px; vertical-align: middle; margin: 0 0 0 11px; }

    #ee-lite-security
    { width: 30px; height: 31px; background-position: 0 -103px; }

    #ee-lite-contacts
    { width:28px; height:29px; background-position:0 -140px; }

    #ee-lite-social
    { width:27px; height:29px; background-position:0 -176px; }

    .ee-lite-features .show-hide-feature, .ee-lite-features .minus
    { background: url('/getImage.php?src=/eelite-features-accordion.png') no-repeat 0 -4px; cursor: pointer;
      display: inline-block; float: right; height:19px; position: absolute; right: 14px; top:15px; width: 19px; z-index: 1001;
    }

    .ee-lite-features .minus
    { background-position: 0 0; height: 1px; top:24px; }

    .ee-lite-features ul li:first-child h3 .show-hide-feature, .ee-lite-features ul li:nth-child(5) .show-hide-feature
    { top:18px; }

    .ee-lite-features ul li:first-child h3 .minus, .ee-lite-features ul li:nth-child(5) .minus
    { top:27px; }

    .ee-lite-features ul li { width: 100%; }

    .ee-lite-submit-wrapper { height: auto; padding: 20px 0 15px 0; }

    .ee-lite-total-wrapper { padding: 18px 0 0 0; }

    .ee-lite-banner-links { z-index: 999; }
}


/* Tablet View (above 768px and below 1000px) */

@media screen and (min-width: 768px) and (max-width: 1000px) {

    .ee-upsell { display: none; }

    #ee-lite-wrapper, .ee-lite-banner, .ee-lite-banner-content, .ee-lite-plans, .ee-lite-features, .ee-lite-faqs
    { width: 100%; margin: 0; padding: 0; }

    #ee-lite-wrapper { margin: -20px 0 0 0; }

    /* Banner */

    #ee-lite-wrapper h1
    { font-family: 'Open Sans', sans-serif !important; font-size: 26px; font-weight: bold;
      line-height: 26px; padding: 50px 0 0 0;
    }

    .ee-lite-banner p { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400; margin: 2px 0 0 0; }

    .ee-lite-banner p.ee-lite-highlights span { font-size: 18px; margin: 0 22px; }


    /* Banner Links */
    .ee-lite-banner-links .rfloat
    { margin-right: 20px; width: 20%;  }

    .ee-lite-banner-links .lfloat
    { margin: 28px 0 28px 20px; }

    .ee-lite-banner-links p .white-text
    { font-family: 'Open Sans', sans-serif; font-size: 24px !important; font-weight: 400 !important; }



    /* Plans */

    .ee-lite-plans { position: relative; }

    .ee-lite-plans h2 { margin:43px auto -5px auto; width: 470px; }

    #enterpriseemail_lite_form { margin: 0 auto; width: 94%; }

    #ee-lite-wrapper label
    { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400;
      display: inline-block; width: 100%; white-space: nowrap
    }

    #ee-lite-wrapper input, #ee-value
    { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; color:#1b1b1b; }

    #ee-lite-wrapper input
    { width: 100%; padding: 6px 0; box-shadow: none; border:1px solid #D3D3D3; }

    .ee-lite-input-wrapper
    { width: 17%; }

    .ee-lite-input-wrapper
    { padding: 28px 0 25px 3%; }

    .ee-lite-select-wrapper
    { padding: 16px 0 40px 3.9%; width: 27.5%; }

    #ee-value {  box-shadow: none; border:1px solid #D3D3D3; }

    .ee-lite-total-wrapper
    {  padding: 16px 16px 40px 3.9%;  width: 17.5%;  margin: 0 0 0 -5px }

    .ee-lite-total-wrapper .total-value
    { color: #1b1b1b; font-family: 'Open Sans', sans-serif; font-size: 16px; font-weight: 600; width: 130px; }

    .ee-lite-submit-wrapper { width: 172px !important; height: 139px; position: absolute; right:0; }

    .ee-lite-submit-wrapper #purchase_button { font-size: 15px; height: 35px; line-height: 35px; width: 108px; }

    .ee-lite-plan-details em
    { color: #434343; font-family: 'Open Sans', sans-serif; font-size: 10px !important; font-weight: 400; line-height: 15px;
      display: block; width: 150px;
    }

    .ee-lite-plan-divider { left: auto; right: 172px; height: 139px; }


    .ee-lite-features ul, .ee-lite-faqs ul
    { margin: 0 auto; width: 96.5% !important; }

    .ee-lite-features h2, .ee-lite-faqs h2, .ee-lite-plans h2
    { background: none; font-family: 'Open Sans', sans-serif !important; font-size: 26px;font-weight: bold; }

    .ee-lite-plan-details { margin: 10px 0 0 10px; width: 150px; }

    .ee-lite-submit-wrapper #purchase_button { margin: 35px 0 0 35px }

    /* Features */

    .ee-lite-features ul { text-align: center; }

    .ee-lite-features h2
    { margin: 44px 0 26px 0; }

    .ee-lite-features ul li
    { margin: 0 55px 26px 0; width: 27%; }

    .ee-lite-features ul li h3
    { font-family: 'Open Sans', sans-serif; font-size: 16px; font-weight: 600; }

    .ee-lite-features ul li p
    { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; line-height: 21px; }


    /* FAQ's */
    .ee-lite-faqs ul
    { border: 1px solid #ddd; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; }

    .ee-lite-faqs ul li
    { border-bottom: 1px solid #ddd; margin: 0 !important; padding: 10px 0 0 0; position: relative; text-align: left; }

    .ee-lite-faqs ul li:last-child
    { border-bottom: 0; }

    .ee-lite-faqs ul li h4
    {   display:inline-block;
        font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400;
        margin:0; line-height: 21px; text-align: left; vertical-align: middle; width: 723px; padding: 15px 0px 14px 18px  !important;
    }

    .ee-lite-faqs ul li p
    {   border-top:1px solid #ddd; font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400;
        line-height: 18px; margin: 0; padding: 10px 12px 10px 12px; text-align: left; display: none;
    }

    .ee-lite-faqs .show-hide-feature, .ee-lite-faqs .minus
    { background: url('/getImage.php?src=/eelite-features-accordion.png') no-repeat 0 -4px; cursor: pointer;
        display: inline-block; height:19px; position: absolute; right: 20px; top:11px; width: 19px; z-index: 1001;
    }

    .ee-lite-faqs .minus
    { background-position: 0 0; height: 1px; top:20px; }


    /* Select Domain Modal */

    #select-domain-modal { margin-left: -345px; }

    .ee-lite-banner-links { z-index: 999; }


}/* Mobile View (above 320px and below 768px) */

@media only screen and (min-width: 320px) and (max-width: 767px) {

    /* Common */

    .sh-hosting-wrapper .inline-block.sh-mbg,
    .sh-hosting-features-content h2 span.left-sep,
    .sh-hosting-includes h2 span.left-sep,
    .sh-hosting-faqs h2 span.left-sep,
    .sh-hosting-wrapper .hosting-select:before,
    .sh-hosting-features-content h2 span.right-sep,
    .sh-hosting-includes h2 span.right-sep,
    .sh-hosting-faqs h2 span.right-sep,
    .sh-hosting-wrapper .hosting-select:after,
    .sh-pricing-data-content .lfloat div.sh-mbg,
    .sh-hosting-faqs,
    .banner-addons-wrapper
    { display: none; }

    .sh-hosting-meta-content,
    .sh-pricing-data-content,
    .sh-hosting-features-content,
    .sh-hosting-includes-content,
    .sh-hosting-faqs-content,
    .sh-hosting-wrapper .country_specific_tabs,
    .sh-hosting-wrapper .server_loc_tabs,
    .money-back-note,
    .sh-hosting-wrapper .ui-subheading.hosting-select
    { margin: 0 auto; width: 87.5%; }

    /* Containers */

    .sh-hosting-banner h1,
    .sh-hosting-features-content h2,
    .sh-hosting-incudes-content h2,
    .sh-hosting-includes h2,
    .sh-hosting-faqs h2,
    .sh-hosting-wrapper h2.hosting-select,
    .sh-hosting-wrapper h2.hosting-select em
    { font-family: 'Open Sans', sans-serif; font-size: 20px; font-weight: bold; }


    /* Banner */

    span.left-text, span.right-text
    { font-size: 22px; text-align: left;  line-height:28px;  top:0; }

    span.left-text
    { left: 9%; width: 90px; }

    span.right-text
    { right: 6%; width: 123px; }

    .sh-hosting-banner-content { margin: 0 auto; width: 98.75%; }

    .sh-hosting-banner h1
    { margin: 0 0 9px 0; }

    .sh-hosting-banner-content p{ font-size: 14px; }

    .sh-hosting-banner-content .switch.windows { margin: 17px auto 0 auto; }

    .sh-hosting-banner-content .sh-banner-img
    {  background: url("/getImage.php?src=sh-hosting-banner-img-mobile.png") no-repeat; margin: 27px auto 0 auto; height: 215px; width: 316px; }

    /* Meta */

    .sh-hosting-meta { padding: 15px 0; }

    .sh-hosting-meta-content p { font-family:'Open Sans', sans-serif; font-size: 14px; font-weight: bold; color:#000; }

    .sh-hosting-meta-content .sub-text { font-family:'Open Sans', sans-serif; font-size:14px; font-weight: 400; margin: 8px 0 7px 0; }

    .sh-pricing-data-content .lfloat, .sh-pricing-data-content .rfloat { float: none; display: block; text-align: center; }

    .sh-pricing-data-content span { font-family:'Open Sans', sans-serif; font-size:20px; font-weight:600; }

    .sh-hosting-meta-content .sh-hosting-supports
    { background: url("/getImage.php?src=sh-hosting-supports-mobile.png") no-repeat; height: 120px; margin: 5px auto 10px auto; width: 280px; }

    .sh-pricing-data-content .lfloat
    { margin: 0 10px }

    .sh-pricing-data-content .lfloat div {  margin: -4px 0 -13px 0;  }

    .sh-pricing-data-content .view-plans { font-size: 15px; padding: 9.5px 19.5px; }

    .sh-pricing-data-content span.value
    { font-family: 'Open Sans', sans-serif; font-size: 23px; font-weight: 600;  }

    .sh-pricing-data-content span.symbol, .sh-pricing-data-content span.valid
    { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400;  }

    .sh-pricing-data-content span.valid
    { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400;  }

    .sh-pricing-data-content .view-plans { margin: 20px 0 14px 0; }

    .sh-pricing-data { background: #222; }

    /* Features & Includes */

    .sh-hosting-features-content h2
    { margin: 16px auto 5px auto; }

    .sh-hosting-features-content ul { margin-bottom: -14px; }

    .sh-hosting-features-content ul li,
    .sh-hosting-includes-content div.includes
    { padding: 0; position: relative; margin: 0; width: 100%; }

    .sh-hosting-includes-content div.includes, .sh-hosting-features-content ul li, .sh-hosting-includes-content div.includes:last-child
    { border: 1px solid #ddd; }

    .sh-hosting-features-content ul li { text-align: left; margin-bottom: -1px  }

    .sh-hosting-features-content ul li:first-child, .sh-hosting-includes-content div.includes:first-child
    { border-radius: 2px 2px 0 0; -moz-border-radius: 2px 2px 0 0; -webkit-border-radius: 2px 2px 0 0; }

    .sh-hosting-features-content ul li:last-child, .sh-hosting-includes-content div.includes:last-child
    { border-radius: 0 0 2px 2px; -moz-border-radius: 0 0 2px 2px; -webkit-border-radius: 0 0 2px 2px; }

    .sh-hosting-features-content ul li .feature-icon,
    .sh-hosting-features-content ul li h3
    { display: inline-block; vertical-align: middle; }

    .sh-hosting-features-content ul li p { font-size: 14px; text-align: left; padding: 12px; }

    .sh-hosting-features-content ul li .feature-icon {
        background: url('/getImage.php?src=images/sh-features-icons.png') no-repeat;
        background-size: 98px 30px;
        margin: 12px 10px 12px 11px;
    }
    .sh-hosting-features-content ul li .feature-icon.speed { background-position: 0 0; height: 30px; width: 31px; }

    .sh-hosting-features-content ul li .feature-icon.email {  background-position: -36px 0; height: 30px; width: 31px; }

    .sh-hosting-features-content ul li .feature-icon.cpanel {  background-position: -70px 0;  width: 31px;  height: 30px;  }

    .sh-hosting-features-content ul li .feature-icon.uptime {
        background: url('/getImage.php?src=images/sh-windows-uptime.png') no-repeat; background-size: 26.5px 30px;
        height: 30px; margin: 12px 10px 12px 11px;  width: 31px;
    }

    .sh-hosting-features-content ul li h3,  .sh-hosting-includes-content h3
    { font-size: 14px; font-weight: 400; }

    .sh-hosting-features-content ul li h3
    { margin: 0; }

    .sh-hosting-features-content ul li p
    { border-top: 1px solid #ddd; }

    .sh-hosting-features-content ul li p, .sh-hosting-includes-content ul li span
    { font-size: 14px; font-weight: 400; line-height: 18px; }

    .sh-hosting-features-content ul li p, .sh-hosting-includes-content ul
    { display: none; }

    .sh-hosting-includes { padding: 0 0 30px 0; margin-bottom: -50px; }

    .sh-hosting-includes h2 {  padding: 25px 0;  margin: 0;  }

    .sh-hosting-includes-content div.includes { min-height: 0; margin: -1px auto 0 auto;  border-bottom:0  }

    .sh-hosting-includes-content h3 { padding: 16px 0 16px 10px; border-bottom:1px solid #ddd; }

    .sh-hosting-includes-content ul {  margin: 6px 13px 6px 28px; border-bottom:1px solid #ddd;  }

    .sh-hosting-includes-content ul:last-child  { border-bottom: 0; }

    .sh-content-toggle
    { background: url('/getImage.php?src=sh-feat-inc-toggle-mobile.png') no-repeat;
        position: absolute; right:13px; top:15px; height: 19px; width: 19px;
    }

    .sh-content-toggle.minus { height: 1px; background-position: 0 -9px; top:25px; }

    .sh-hosting-includes-content div.includes:last-child h3 { margin-bottom: -1px; }


    /* Plans */

    .sh-hosting-wrapper .server_loc_tabs { margin: 11px 0 25px 0; text-align: center; width: 100%; }

    .sh-hosting-wrapper #plans-container br { display: none; }

    .sh-hosting-wrapper .server_loc_tabs li
    {   background-position: 8px 14px; background-size: 16px 11px;
        font-family: 'Open Sans', sans-serif;  font-size: 12px;  font-weight: 600;
        padding: 0 10px 0 28px;
    }

    .sh-hosting-wrapper .plans-columns-wrp .sliderbullets { width: 100%; text-align: center; margin: 10px auto 0 auto; }

    .sh-hosting-wrapper .plans-columns-wrp .sliderbullets li
    {  background: #ccc; display: inline-block; height:10px; margin: 0 8px 0 0; width: 10px;
        border-radius: 20px;  -moz-border-radius: 20px; -webkit-border-radius: 20px; cursor: pointer;
    }

    .sh-hosting-wrapper .plans-columns-wrp .sliderbullets li.current-slide { background: #0893d8; }

    .sh-hosting-wrapper .plans-columns-wrp .sliderbullets li:last-child { margin: 0;  }

    .sh-hosting-wrapper .plans-columns-wrp .plans-columns { position: relative; }

    .sh-hosting-wrapper .plans-columns-wrp .plans-columns .prev,
    .sh-hosting-wrapper .plans-columns-wrp .plans-columns .next
    { cursor: pointer; position: absolute; top:62.3%; height: 26px; width: 26px; z-index: 999; }

    .sh-hosting-wrapper .plans-columns-wrp .plans-columns .prev
    { left: 8px; background: url('/getImage.php?src=sh-slider-prev.png') no-repeat #fff; }

    .sh-hosting-wrapper .plans-columns-wrp .plans-columns .next
    { right: 8px; background: url('/getImage.php?src=sh-slider-next.png') no-repeat #fff; }

    .sh-hosting-wrapper .plans-columns-wrp .plans-columns .prev.leftendofslider,
    .sh-hosting-wrapper .plans-columns-wrp .plans-columns .next.rightendofslider
    { background: none; }

    .sh-hosting-wrapper .plan-list { width: 87.5%; }

    .sh-hosting-wrapper .p-feat, .sh-hosting-wrapper .p-feat strong, .sh-hosting-wrapper li.p-dropdown select { font-size: 14px; }

    .sh-hosting-wrapper li.p-dropdown select { width: 100%; }

    .sh-hosting-wrapper .p-name { font-size: 18px; padding: 13px 0 12px 0; }

    .sh-hosting-wrapper .plan-list { margin: 0; }

    .sh-hosting-wrapper .p-pricing { font-size: 24px; font-weight: 600; padding: 15px 0; }

    .sh-hosting-wrapper .p-duration { text-transform: lowercase; }

    .sh-hosting-wrapper .p-currency { font-size: 15px; font-weight: 400; top:-4px; }

    .sh-hosting-wrapper .server_loc_tabs ul li.sel .sel_tab { left:20px !important;  }

    .sh-hosting-wrapper .server_loc_tabs ul li.sel:last-child .sel_tab { left:12.5px !important;  }

    .sh-hosting-wrapper .server_loc_tabs ul li.sel:first-child .sel_tab { left:25px !important;  }

    .money-back-note p, .money-back-note span.money-back-icon
    { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; line-height: 21px; }

    .money-back-note p
    { margin: 7px 0 0 0; width: 81%; }

    .money-back-note span.money-back-icon { margin: 0 6px 0 0; }

    .sh-hosting-wrapper .p-button .txt-button,
    .sh-hosting-wrapper .plan-featured li.p-button .txt-button
    { width: 92%;  padding: 8px 0;  font-size: 15px; }

    /* Hide prev for 1st plan - hack */

    .hidefirstprev
    { background: #fff; display: block; height: 28px; left: -32px; position: absolute; top:58.3%; width: 46px; z-index: 999;  }

    .hidefirstprev .onepxborder
    { display: block; width: 1px; background: #dedcdc; height: 28px; margin-left: 31px; }

}

/* Tablet View (above 768px and below 1000px) */

@media screen and (min-width: 768px) and (max-width: 1000px) {

    /* Common */

    .sh-hosting-wrapper .inline-block.sh-mbg,
    .sh-hosting-features-content h2 span.left-sep,
    .sh-hosting-includes h2 span.left-sep,
    .sh-hosting-faqs h2 span.left-sep,
    .sh-hosting-wrapper .hosting-select:before,
    .sh-hosting-features-content h2 span.right-sep,
    .sh-hosting-includes h2 span.right-sep,
    .sh-hosting-faqs h2 span.right-sep,
    .sh-hosting-wrapper .hosting-select:after,
    .money-back-note
    { display: none; }

    /* Containers */

    .sh-hosting-banner-content,
    .sh-hosting-meta-content,
    .sh-pricing-data-content,
    .sh-hosting-features-content,
    .sh-hosting-includes-content,
    .sh-hosting-faqs-content,
    .sh-hosting-wrapper .country_specific_tabs,
    .sh-hosting-wrapper .server_loc_tabs,
    .money-back-note,
    .sh-hosting-wrapper .ui-subheading.hosting-select
    { margin: 0 auto; width: 95%; }


    /* Banner, Features & Includes */

    span.left-text, span.right-text
    { font-size: 29px; line-height:35px;  top:20px; }

    span.left-text
    { left: -10px; width: 120px; }

    span.right-text
    { right: -10px; width: 160px; }


    .sh-hosting-banner-content .sh-banner-img
    { background: url("/getImage.php?src=sh-hosting-banner-img-tablet.png"); height: 218px; margin: 26px auto 0 auto; width: 730px; }

    .banner-addons-content
    { background: url("/getImage.php?src=banner-addons-bg-tablet.png") no-repeat; width: 730px; }


    .sh-hosting-wrapper.windows .banner-addons-content
    { background: url("/getImage.php?src=banner-addons-bg-windows-tablet.png") no-repeat; }

    .addon-txt.left { left: 63px; }

    .addon-txt.right { right: 75px; }

    .sh-hosting-meta-content .sh-hosting-supports
    { background:url("/getImage.php?src=sh-hosting-supports-tablet.png") no-repeat; height: 72px; margin: 17px auto 0 auto; width: 728px;  }

    .sh-hosting-features-content ul li
    { margin: 0 7.5% 0 0;  width: 28%;  }

    .sh-hosting-includes { text-align: center; }

    .sh-hosting-includes-content h3 { text-align: left; }

    .sh-hosting-includes-content div.includes, .sh-hosting-includes-content div.includes:first-child
    { border-right: 0; width: 27%; margin: 0 8.5% 0 0; padding: 0; }

    .sh-hosting-includes-content div.includes:last-child{ margin: 0; }

    .sh-hosting-wrapper .plan-list, .sh-hosting-wrapper .plan-list.plan-featured { width: 23%; margin: 0 1% 0 0; vertical-align: top; }

    .sh-hosting-banner h1
    { font-family: 'Open Sans', sans-serif; font-size: 26px; font-weight: bold; padding: 48px 0 0 0; line-height: 26px; }

    .sh-hosting-banner-content p
    { font-family:'Open Sans', sans-serif; font-size:15px; font-weight: 400; margin: 9px 0 2px 0; }

    .sh-hosting-meta-content p { font-size: 18px; margin: -12px auto 0 auto }

    .sh-hosting-meta-content .sub-text { font-size: 16px; margin: 9px 0 24px 0; }

    .sh-hosting-meta-content .sh-hosting-supports { margin: 0 auto; }

    .sh-hosting-meta { padding: 30px 0 0 0; }

    .sh-pricing-data-content span { font-size: 24px; }

    .sh-mbg span, .sh-mbg strong
    {  color: #cccbcb; font-family: 'Open Sans', sans-serif; font-size: 12px; font-weight: 400; line-height: 18px;  }

    .sh-mbg strong, .sh-mbg .d
    { font-weight:400; }

    .sh-pricing-data-content span.symbol
    { margin: 0 0 0 6px; position: relative; top:4px; }

    .sh-pricing-data-content .lfloat { margin: 0; }

    .sh-pricing-data-content .lfloat .inline-block { margin: 15px 0 0 0; }

    .sh-pricing-data-content .lfloat .inline-block span { margin-top: 0; }

    .sh-hosting-features-content h2,
    .sh-hosting-incudes-content h2,
    .sh-hosting-includes h2,
    .sh-hosting-faqs h2,
    .sh-hosting-wrapper h2.hosting-select,
    .sh-hosting-wrapper h2.hosting-select em
    { font-family: 'Open Sans', sans-serif; font-size:22px; font-weight: bold; }

    .sh-hosting-features-content h2 { margin: 35px auto -5px auto; }

    .sh-hosting-features-content ul li h3,
    .sh-hosting-includes-content h3
    { font-size: 16px; }

    .sh-hosting-features-content ul li h3 { margin: 0 0 10px 0; }

    .sh-hosting-features-content ul li .feature-icon
    { margin: 9px auto 17px auto }

    .sh-hosting-includes-content ul li { padding: 0 0 7px 0; }

    .sh-hosting-features-content ul { margin: 0 0 -21px 0; }

    .sh-hosting-features-content ul li p,
    .sh-hosting-includes-content ul li span
    { font-size: 14px; }

    .sh-hosting-includes h2
    { padding: 25px 0 21px 0; }


    /* Plans */
    .server_loc_tabs li.tab_US,
    .server_loc_tabs li.tab_UK,
    .server_loc_tabs li.tab_TR,
    .server_loc_tabs li.tab_HK,
    .server_loc_tabs li.tab_IN,
    .server_loc_tabs li.tab_CN
    { background-position: 10px 13px; }


    .server_loc_tabs li.CN {
        background: url("/getImage.php?src=ic-small-cn.png") no-repeat 10px 13px #fff;
    }

    .sh-hosting-wrapper .server_loc_tabs li { background-size: 18px 13px; font-size: 13px; padding: 0 10px 0 40px; }

    .sh-hosting-wrapper .p-name { font-size: 16px; }

    .sh-hosting-wrapper .p-feat,
    .sh-hosting-wrapper .p-feat strong,
    .sh-hosting-wrapper li.p-dropdown select
    { font-size: 14px; padding: 0 0 12px 0; margin:0; }

    .sh-hosting-wrapper li.p-dropdown select
    { padding: 0 20px 0 10px; width: 98%; }

    .sh-hosting-wrapper li.p-dropdown
    { margin: 3px 13px 0 13px; }

    .sh-hosting-wrapper .p-button .txt-button,
    .sh-hosting-wrapper .plan-featured li.p-button .txt-button
    { font-size: 14px; font-weight: 400; padding: 8px 0; width:91%; }

    .sh-pricing-data-content .view-plans
    { padding: 0; width: 135px; height: 50px; text-align: center; line-height: 50px; }

    .sh-hosting-wrapper .p-pricing { font-size: 24px; }

    .sh-hosting-wrapper .p-duration { font-size: 12px; text-transform: lowercase; }

    .sh-hosting-wrapper .p-currency { font-size: 14px; top:-5px; }

    .sh-hosting-wrapper #plans-container .plans-columns
    { margin: 35px auto 10px auto; }

    .sh-hosting-wrapper .ui-subheading.hosting-select
    { margin: 25px auto -14px auto; }

    /* FAQs */
    .sh-hosting-faqs-content h4,
    .sh-hosting-faqs-content p
    { font-size: 14px; }

    .sh-hosting-faqs h2
    { margin: 35px auto 27px auto; padding: 0; }



}/* Mobile & Tablet Common */

@media only screen and (min-width: 320px) and (max-width: 767px), (min-width: 768px) and (max-width: 1000px){

    /* Common */
    .price-list-bar,
    #newgtld-promo-banner-large,
    .shadow-div,
    .dca-footer-banner,
    .secondary-section .filters,
    .dca-hide-disabled
    {
        display: none !important;
    }

    #myform .div-spacer { display: none; }

    #myform { margin-bottom: -23px; }

    .cart { display: none; }

    .row-gray { background: #fff; border-bottom: 0; }

    .row-indent { padding: 0 20px; width: 87.5%; margin: 0 auto; }

    /* Domain Registration page - Search Tab */

    .tabs-wrapper { background: none; }

    .tabs-wrapper ul.tab
    { background: #fff; border:1px solid #ccc; border-radius: 25px; -moz-border-radius: 25px; -webkit-border-radius: 25px;
      padding: 0; position: relative; margin: 10px 0 0 0; text-align: left; width: 280px;
    }

    .tabs-wrapper .tab li
    { display: inline-block; padding: 12px 15px 10px 15px; margin: 0; cursor: pointer; }

    .tabs-wrapper .tab li a
    { background: none; border:0; color: #1b1b1b; font-family: 'Open Sans', sans-serif; font-size: 16px; font-weight: 500; }


    .tabs-wrapper .tab li#tab-for-dbox
    { border-right: 1px solid #ccc; margin: 0 15px 0 0; }

    .tabs-wrapper .tab li.active a
    { color: #377ce4; padding: 0; }

    .tabs-wrapper .tab li a
    { padding: 0; }

    .tabs-wrapper .tab li.active:after
    { content: ""; background: url(/getImage.php?src=active-tab-pointer.png) no-repeat; bottom: -10px;
      display: block; left: 20%; position: absolute; height: 10px; width: 18px;
    }

    .tabs-wrapper .tab li.active:after {  left: 62px; }

    .tabs-wrapper .tab li#tab-for-idn.active:after
    { left: 72% }

    /* Domain Registration page - Search Box */

    #ndomins-list, #txt_idn_domain_name
    {  height: 145px !important; outline: none; resize: none; width: 100%;  -moz-appearance: none; -webkit-appearance: none; outline:0px none transparent; }

    .domain-sbox, .tld-box {
        width: 275px;
        max-width: 275px;
    }

    .domain-sbox, .tld-box {
        display: block;  float: none;  padding: 0;  margin: 0 auto;
    }

    .domain-sbox { padding: 3px 0 0 0; }

    #idn_domain_ca_form .placeholder { padding: 20px 18px; }

    .placeholder-wrapper {
        min-height: 187px;  width: 273px; border: 2px solid #eee; border-radius: 6px; -webkit-border-radius: 6px; -moz-border-radius: 6px;
    }

    .placeholder-txt1
    { color: #2a2a2a; font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 500; line-height: 26px; margin-bottom: 4px; }

    .placeholder-txt2
    { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; color: #bbb; line-height: 18px; width: 225px; }

    #idn_domain_ca_form .txt-green
    { color:#6fc61e }


    #idn_domain_ca_form .tld-box.rfloat
    {  margin: 5px 0 0 0;  }

    #idn_domain_ca_form .tld-box.rfloat .list-heading
    { margin:18px 0 3px 0; }

    #idn_language
    { margin: 14px 0 0 0;
        -webkit-appearance: none;  appearance:none; -moz-appearance: none;
        width:226px; background: url(/getImage.php?src=idn_select_box.png) no-repeat right 2px center #fff;
        border:1px solid #ddd;  outline:none;
        font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400; color:#a7a7a7; padding:0 0 0 12px;
        box-shadow: none; -webkit-box-shadow: none; -moz-box-shadow: none; height: 38px; line-height: 38px;
     }

    .sd-tarea
    {
        border: 1px solid #ddd; border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px;
        box-shadow: none; -moz-box-shadow: none; -webkit-box-shadow: none;
    }

    h2.ui-heading, h2.ui-heading span.lite-green
    {  color: #1b1b1b; font-family: 'Open Sans', sans-serif;  font-size: 20px;  font-weight: bold; line-height: 26px;  }

    h2.ui-heading { margin: 10px 0 0 0; }

    .row-gray h2.ui-heading { margin: 0 auto; width: 280px; }

    .tld-box.rfloat .list-heading
    { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 500; color:#1b1b1b; margin: 5px 0 3px 0; }

    input[name="Checkout"].uiButton,
    #whois input.uiButton
    {
        background: #0893d8; border: 1px solid #044bbc;
        border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px;
        -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 1px rgba(216, 216, 216, 0.2);
        -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 1px rgba(216, 216, 216, 0.2);
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 1px rgba(216, 216, 216, 0.2);
        font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400;
        height: 34px; line-height: 34px; outline: none; padding: 0; width: 277px;
    }

    .dataTable2 label
    {  font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; color:#1b1b1b;  }

    .dataTable2 input[type="checkbox"]
    { position: relative; top:3px;  }

    .domain-action .alink, .dataTable2 a
    { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; text-decoration: underline; }

    #sel-all, #show-tlds { margin-right: -3px; }


    /* Other TLDs */

    .tld-box table.dataTable2 tr { display: inline; }

    .tld-box table.dataTable2 td { width: auto }

    .tld-box table.dataTable2 td a { position: relative; top: -5px; }

    .tld-box table.dataTable2 td.radio {  display: inline-block;  width: 32%;  vertical-align: top;  }

    .dataTable2 label { width: 78%; vertical-align: top;  word-wrap: break-word;  display: inline-block; }

    .tld-box table.dataTable2 td.wfix { display: block; width: 100%; }

    /* Domain Registration page - IDN search form */

    .hastab.tabs-wrapper {
        margin: 0 auto 27px auto !important;
        width: 280px;
    }

    .hastab.tabs-wrapper .tab {
        padding-left: 0 !important;
    }

    #idn_domain_ca_form .tld-box.rfloat
    { margin: 5px auto 0 auto; }



    /* Domain Registration page - Features */

    p.fea-heading
    { color: #1b1b1b; font-family: 'Open Sans', sans-serif; font-size: 16px; font-weight: 400; line-height: 22px;
      margin: -3px 0 23px 0;
    }

    .feature-blurb ul { width:100%; }

    .feature-blurb ul:nth-child(2) { margin-top: -1px; }

    .feature-blurb ul li
    { background: none !important; padding: 0; border: 1px solid #ddd; border-bottom: 0; margin-bottom: -1px; width: 99%; }

    .feature-blurb ul li:last-child
    { margin-bottom: 0; }

    .feature-blurb ul li .list-info
    { border-bottom: 1px solid #ddd; color:#000; display: none;
      font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; line-height: 18px;
      padding: 7px 10px 10px 10px; width: 94%;
    }

    .feature-blurb ul li .title
    { border-bottom: 1px solid #ddd; font-family: 'Open Sans', sans-serif; min-height: 50px; line-height: 50px;
      font-size: 14px; font-weight: 400; color:#000; padding: 0; position: relative;
    }

    .feature-blurb ul li .title .feat-icon, .feature-blurb ul li .title .show-hide-feature
    { display: inline-block; background: url(/getImage.php?src=domain-reg-features-mobile.png) no-repeat 0 0;
      margin: 0 11px; vertical-align: middle; width: 33px;
    }

    .feature-blurb ul li.ic-1 .feat-icon
    {  background-position: 0 0; height: 30px; }

    .feature-blurb ul li.ic-3 .feat-icon
    { background-position: 0 -110px; height: 22px; }

    .feature-blurb ul li.ic-4 .feat-icon
    { background-position: 0 -51px; height: 38px; }

    .feature-blurb ul li.ic-5 .feat-icon
    { background-position: 0 -209px; height: 31px; }

    .feature-blurb ul li.ic-6 .feat-icon
    { background-position: 0 -158px; height: 29px; }

    .feature-blurb ul li.ic-7 .feat-icon
    { background-position: 0 -314px; height: 29px; }

    .feature-blurb ul li.ic-8 .feat-icon
    { background-position: 0 -263px; height: 26px; }

    .feature-blurb ul li.ic-1 .list-info { display: block; }

    .feature-blurb ul li.ic-8 .list-info { border-bottom: 0; }

    .feature-blurb ul li.ic-8 .title { margin-bottom: -1px }

    .feature-blurb ul li.ic-8 { border-bottom: 1px solid #ddd; }

    .feature-blurb ul li .title .show-hide-feature, .feature-blurb ul li .title .minus
    { background: url('/getImage.php?src=/eelite-features-accordion.png') no-repeat 0 -4px; cursor: pointer;
        display: inline-block; height:19px; position: absolute; right: 1px; top:15px; width: 19px; z-index: 1001;
    }

    .feature-blurb ul li .title .minus
    { background-position: 0 0; height: 1px; top:25px; right: 12px; }


    /* DCA page - Privacy protection */

    #pp_modal_div
    { z-index: 9999; }

    #pp_modal_div .modal_content#upsell_modal
    {  background: #626262; border: solid 1px #828282;  padding: 4px;left: 50%; margin-left: -44.5%; width: 87.5%; }

    #pp_modal_div .modal_content#upsell_modal .model-indent
    { margin: 0; padding: 20px; }

    #pp_modal_div .grd-table { margin: -15px 0 0 0; }

    #pp_modal_div .grd-table.dlist-sel { margin: 0 0 15px 0; }

    #pp_modal_div .modal_content p, #pp_modal_div .grd-table td
    { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; color: #1b1b1b; line-height: 18px; }

    #pp_modal_div .grd-table td { line-height: 16px; }

    #pp_modal_div .modal_content#upsell_modal .txt-info
    { font-family: 'Open Sans', sans-serif; font-size: 11px; font-weight: 400; color: #979797; line-height: 15px; }

    #pp_modal_div .modal_content#upsell_modal #pp_not_supported
    { display: block; margin: 0 0 -5px 0;  }

    #pp_modal_div .modal_content#upsell_modal .grd-table th
    { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400; color: #1b1b1b; }

    #pp_modal_div .modal_content#upsell_modal .grd-table th .txt-m.green
    { font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; color: #6fc61e; }

    #pp_modal_div .modal_content#upsell_modal .heading
    { background: none; font-family: 'Open Sans', sans-serif; font-size: 18px; font-weight: 600;
        padding: 15px 0 0 20px; margin: 0 0 -10px 0; color: #000; line-height: 26px;  }

    #pp_modal_div .modal_content#upsell_modal .model-twocols
    { background: #fff; border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px; }

    #pp_modal_div .modal_content#upsell_modal .model-twocols .ui-button
    {
        background: #0893d8; border: 1px solid #044bbc; border-top: 1px solid #066ad3;
        font-family: 'Open Sans', sans-serif; font-size: 13px; font-weight: 400; color: #fff;
        border-radius: 4px; -moz-border-radius: 4px; -moz-border-radius: 4px;
        height: 29px; line-height: 29px;  margin: 15px 0; padding: 0; width: 118px;
    }

    #pp_modal_div .modal_overlay { position: fixed; }

    .ic-big-privacy
    { background: url(/getImage.php?src=/pp-modal-heading-bg.png) no-repeat;
        margin: 5px 5px 0 0; padding: 0; height: 16px; vertical-align: top; width: 15px; }

    #pp_modal_div .grd-table  { table-layout: fixed; }

    #pp_modal_div .grd-table td, #pp_modal_div .grd-table th
    { width: 140px; }

    #pp_modal_div .grd-table td,
    #pp_modal_div .grd-table th
    { max-width: 50%; width: 50%; padding: 15px; word-wrap: break-word; }

    #pp_modal_div .modal_content#upsell_modal a.modal_close#close_pp_modal
    { font-size: 0; display: block; background: url("/getImage.php?src=/images/close.png") no-repeat 0 0;
        height:15px; position: absolute; width:15px; opacity:0.8;
    }

    #pp_modal_div .wide_modal .model-twocols
    { max-width: none; }

    /* DCA Page - Shopping Cart */

    .cart.rfloat
    { float: none; background: #fff; min-height: 0; width: 99.5%; z-index: 999;
        box-shadow: 0 -2px 4px rgba(204, 204, 204, 0.9); position: fixed; bottom: 0; top: auto !important;
    }

    .dca-page-wrapper .cart-header h3
    { font-family: 'Open Sans', sans-serif; font-size: 16px; font-weight: 600; }

    .cart .ppp-modal p a.heading { font-size: 13px; }

    .dca-page-wrapper #checkout
    { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400;
        border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px;
        height: 35px; line-height: 35px; padding: 0; position: absolute; right: 20px; bottom: 20px;  width: 95px;
    }

    .dca-page-wrapper .total-container{ margin: 20px 0 20px 20px; }

    .dca-page-wrapper .cart .ppp-modal { margin-bottom: 0; }

    .dca-page-wrapper .cart-items-container { min-height: 0; }

    .dca-added
    { margin: 0 0 0 -50px; width: 130px }

    .cart-header { position: relative; }

    .cart-items-arrow-toggle
    { background: url(/getImage.php?src=/cart-toggle-arrow.png) no-repeat 0 0; display: block; position: absolute;
        right:20px; top:15px; height: 16px; width: 16px;
    }

    .cart-list, .ppp-modal { display: none; }

    .dca-domain-name span.dca-primary-dn.initial-padding { padding: 0; }

    /* IDN validation error styling */

    .tld-box .highlight { background: none; }

    .tld-box .cursive
    {  background: none;  color: #d84a49;  font: 400 12px 'Open Sans', sans-serif; font-style: normal;  padding: 0;  position: absolute; }


    /* DCA page cart styling - Common */

    .dca-page-wrapper .total-container span.WebRupee
    {  font-family: 'Open Sans', sans-serif;  font-size: 18px;  font-weight: 600;  color: #28af0f; }

    /* Domain Search */

    .secondary-result-section-tld-upsell .original-price
    { position: relative; right: 0; top: 0; font-size: 14px; }


}

/* DCA page - Mobile View (above 320px and below 768px) */

@media only screen and (min-width: 320px) and (max-width: 767px)
{

    /* DCA page - Search form */

    .row-gray h2.ui-heading
    { margin: 5px auto 0 auto; }

    .dca-page-wrapper { margin: 0 auto; padding: 0; width: 100%; }

    .dca-search-result-content
    {  border: 0; width: 87.5%;  margin: 0 auto;  }


    .dca-search-result-content { float: none; }

    .dca-search
    { padding: 1% 1% 1.2% 1%; text-align: center; }

    .dca-search,
    .secondary-section,
    .secondary-result-section,
    .primary-result-section { width: 100%;  }

    .secondary-section { margin: 10px 0 0 0; }

    .secondary-result-section { border: 0; }

    .dca-search input
    { height: 42px; padding: 0 10px 0 12px; vertical-align: top; width: 58%;
        color:#1b1b1b; font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400;
    }

    .dca-search button
    { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400; height: 44px; width: 33%;
        border-radius:2px; -moz-border-radius: 2px; -webkit-border-radius: 2px; padding: 0 2.5%;
    }

    .tabs-wrapper .tab li{ padding: 12px 0 10px 0; text-align: center; }

    .tabs-wrapper .tab li#tab-for-dbox { width: 142px; left: 62px; }

    .tabs-wrapper .tab li#tab-for-idn { width: 105px; left:200.5px; }

    .placeholder {  top: -6px; padding: 15px 20px;  }

    #domain-box, #idn-box {  width: 100%; margin: 0 auto; }

    .domain-sbox
    {  width: 98.6%; max-width: none; margin: 3px 0 0 0; padding: 0; border:2px solid #eee;
       border-radius: 5px; -moz-border-radius:5px; -webkit-border-radius: 5px;
    }

    .placeholder-wrapper { width: 99.3%; border-radius: 4px; -moz-border-radius:4px; -webkit-border-radius: 4px; border:1px solid #ddd;  overflow: hidden; }

    .sd-tarea { border:0; }

    .tld-box, .idomain-box .tld-box { width: 100%;  max-width: none; margin: 12px 0 0 0; }

    .idomain-box .domain-sbox{ margin: 3px 0 12px 0; }

    .tld-box table.dataTable2 { margin: -1px 0 0 0; }

    .tld-box table.dataTable2 td { padding: 5px 0; }

    .placeholder-txt2, input[name="Checkout"].uiButton { width: 100%; }

    h2.ui-heading { margin: 0; }

    input[name="Checkout"].uiButton{ height: 36px; }

    p.fea-heading { font-size: 15px; }


    /* DCA page - Search results */

    .dca-domain-name span.dca-primary-dn.initial-padding { padding: 0; width: 72%; }

    .secondary-result
    { border: 1px solid #ccc; width: 100%; margin: 0 auto 10px auto; padding: 15px  0 0 0; }

    .primary-result-section, .secondary-result
    { border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; }


    .secondary-result .dca-domain-avail, .primary-result .dca-domain-avail
    { position: static; border-top:1px solid #ccc; display: block; height: auto;  padding: 10px 0; width: 90%;  margin: 0 auto; }

    .secondary-result .dca-domain-avail .styled-select, .primary-result .dca-domain-avail .styled-select
    { position: absolute; top:13px; right: 15px; left: auto;
        background: url('/getImage.php?src=images/dca-duration-arrow.png') no-repeat right transparent;
    }

    .primary-result .dca-domain-avail .styled-select,
    .secondary-result .dca-domain-avail .styled-select, .dca-domain-avail span .dca-duration
    { width: 66px !important; text-align: right !important; padding: 0 15px 0 0;  }

    .primary-result .dca-domain-avail .styled-select,
    .primary-result .dca-domain-avail span .dca-duration
    { padding: 0 8px 0 0; }

    .dca-domain-avail span .dca-duration { font-size: 14px; }

    .secondary-result .dca-domain-name, .primary-result .dca-domain-name
    { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400; color: #1b1b1b;
        display: block; height: auto; margin: 0 0 15px 15px; width: 65%;
    }

    .dca-pricing { display: inline-block; text-align: left; white-space: nowrap; width: 85px; }

    .primary-result .dca-domain-avail span.inline-block { margin: 0; }

    .show-more button
    { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400; margin:5px 0 0 0; background-position: 170px -170px; }

    .dca-dn-available-icon,
    .dca-dn-unavailable-icon
    { display: inline-block; height: 20px; margin: 0 7px 0 0; width: 20px; }

    .dca-dn-available-icon
    { background: url(/getImage.php?src=/domain-available-mobile.png) no-repeat; }

    .dca-dn-unavailable-icon
    { background: url(/getImage.php?src=/domain-unavailable-mobile.png) no-repeat; }

    .dca-domain-name span.dca-primary-dn
    { width: 82%; }

    .dca-domain-name span.dca-primary-dn span.namevalue
    { width: 100% }

    .dca-domain-name span.dca-primary-dn span.namevalue
    { font-size: 16px; line-height: 19px; margin-top: -5px; word-wrap: break-word; }

    .primary-result-section { margin: 20px auto 30px auto; }

    .primary-result .dca-domain-name,
    .primary-result .dca-pricing,
    h3.secondary-section-heading
    { font-family: 'Open Sans', sans-serif; font-size: 16px; font-weight: 600; }

    .secondary-section-meta { margin: 0 0 10px 0; }

    .primary-result .dca-domain-name.dca-dn-unavailable { margin: 0 0 0 15px; }

    .dca-page-wrapper .total-container span#total
    { font-family: 'Open Sans', sans-serif; font-size: 20px; font-weight: 600; }

    .primary-result button.pre-add,
    .secondary-result button.pre-add,
    .dca-red-text
    {
        margin: 0 0 0 -25px;
    }

    .dca-premiumdomain-tag .classic-ani
    { display: none !important;  }

    .primary-result .namevalue { position: relative; }

    .primary-result .dca-red-text
    { margin: 0; }

    .secondary-result .dca-domain-name { width: 72%; margin: 0 0 15px 6% }

    .secondary-result .dca-domain-name span { width: 90%; word-wrap: break-word; }

    .secondary-result .dca-domain-avail span.inline-block
    { width: 49%; margin: 0; text-align: left; }

    .primary-result .dca-domain-avail { text-align: left; padding: 10px 0 0 0; }

    .primary-result .dca-domain-avail span.inline-block
    { width: 48% !important; margin: 0;  }

    .secondary-result .dca-domain-avail span.inline-block.dca-select-button,
    .primary-result .dca-domain-avail span.inline-block.dca-select-button,
    .primary-result .dca-domain-avail span.inline-block.styled-select
    { text-align: right; }

    .primary-result .dca-domain-avail ul.hidden-dca-durations,
    .secondary-result .dca-domain-avail ul.hidden-dca-durations
    { left: auto; right: 0; }

    .secondary-result a.tooltipanimated span.classic-ani
    { display: none; }

    .secondary-result .original-price, .primary-result .original-price
    { position: static; }

    .secondary-result .dca-domain-avail span.inline-block em.dca-red-text,
    .secondary-result-section-tld-upsell .dca-domain-avail span.inline-block em.dca-red-text
    { margin: 0; }


    .secondary-result button.select-domain
    {  }

    .primary-result button.select-domain
    {  }


    /* DCA page - Modals */

    #modal_div  .modal_content
    { width: 280px !important; left: 50%; margin-left:-140px; font-family: 'Open Sans', sans-serif; }

    .modal_content
    { z-index: 100001 !important; }

    /* DCA page - Upsell popular TLDs */


    .secondary-result-section-tld-upsell
    { width: 260px; padding: 17px 20px 0 20px; box-shadow: 0 0 4px rgba(204, 204, 204, 1);  }

    .secondary-result-section-tld-upsell-domain
    { margin: 0; padding: 15px 0; }

    .secondary-result-section-tld-upsell-domain-list
    { margin: 0 auto; width: 260px; }

    .secondary-result-section-tld-upsell p
    { font-family: 'Open Sans', sans-serif; font-size: 16px; font-weight: 600; line-height: 21px; padding: 0 0 5px 0;  }

    .secondary-result-section-tld-upsell .dca-select-button { display: block; padding: 0 0 20px 0; }

    .secondary-result-section-tld-upsell .dca-select-button button { margin: 0 0 0 180px; }

    .secondary-result-section-tld-upsell .dca-domain-avail
    { width: 100px; position: relative; right: 0px; top:0;  text-align: right; }

    .secondary-result-section-tld-upsell .styled-select,
    .secondary-result-section-tld-upsell .dca-pricing
    {  position: static !important; margin: 0 !important; padding: 0 !important;  }

    .dca-domain-avail span .dca-duration-upsell { width: 60px; }

    .secondary-result-section-tld-upsell-domain .dca-domain-name
    { font-family: 'Open Sans', sans-serif; font-size: 15px; font-weight: 400; width: 140px; margin: 2px 0 0 8px; }

    .secondary-result-section-tld-upsell .styled-select .dca-duration-upsell
    { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; }

    .secondary-result-section-tld-upsell .dca-pricing
    { margin: 0 0 0 10px; font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; }

    .secondary-result-section-tld-upsell-domain .dca-domain-cb
    { margin: 5px 0 0 0; }

    /* IDN Error Handling */

    .tld-box .cursive
    { top: 287px; left: auto;  }

    /* DCA cart domain name wrapping */

    span.dca-domain-purchased { width: 94%; }


    /* Domain Transfer Page */

    table.data-form { width: 100%; margin: 0 auto;  border:1px solid #ddd; }

    table.data-form td { display: block; margin: 0 auto; padding: 0 5px; width: 87% !important; }

    #secret input.frm-field,
    #whois input.frm-field
    { border:1px solid #ddd; box-shadow: none;
      border-radius: 2px; -moz-border-radius: 2px; -webkit-border-radius: 2px;
      height: 32px; margin: 5px 0 7px 0; padding: 0; width: 100%;   }

    .row-indent .txt-m
    { font: 400 14px/21px 'Open Sans', sans-serif; color:#1b1b1b; text-align: center;  }

    #secret .info-small { font: 400 10px/15px 'Open Sans', sans-serif; color:#434343; margin: 0; padding: 0; }

    #secret .info-small br { display: none; }

    #secret .txt-m, #whois .info-small { font: 400 10px/15px 'Open Sans', sans-serif; text-align: left;   }

    #checkoutButton { margin: 0 0 20px 0; }

    #whois input.uiButton { width: 87%; margin: 0 auto 20px auto; }

    .data-form tr:nth-child(2) { width: 87%; margin: 0 auto; }

    .data-form tr:nth-child(2) td
    {
        display: inline-block; margin: 0 0 0 6%; padding: 0; width: 56% !important;
    }

    .data-form tr:nth-child(2) td:last-child
    {  margin: 0 0 0 5px; width: 31% !important;  }


    #whois .data-form tr:nth-child(2) td:last-child { margin: 0; text-align: center; width: 100% !important; }

    .data-form tr:nth-child(2) td input.frm-field, #tldSelector
    {
        border: 1px solid #ddd; box-shadow: none;
        border-radius: 2px; -moz-border-radius: 2px; -webkit-border-radius: 2px;
        padding: 0 0 0 6%; width: 94%; height: 32px;
    }

    #tldSelector
    { background: url('/getImage.php?src=/sh-hosting-plans-dd.png') #fff no-repeat right 3px center; -moz-appearance: none; -webkit-appearance: none; appearance: none; height: 34px; width: 75px;  }

    .data-form tr:nth-child(2) td:first-child { width:87% !important; display: block !important; }

    .data-form tr:nth-child(2) td:first-child .frm-label,
    #secret .frm-label,
    #whois label
    { font: 400 12px/28px 'Open Sans', sans-serif; color:#1b1b1b; text-transform: uppercase; display: block; text-align: left; }

    #secret .frm-label, #whois label { margin: 10px 0 0 0; }

    .data-form tr:nth-child(2) td:first-child .frm-label { margin: 11px 0 0 0; }


    .data-form tr:nth-child(2) td div { float: none !important; }

}


/* Tablet View (above 768px and below 1000px) */

@media screen and (min-width: 768px) and (max-width: 1000px)
{

    /* Domain Registration */

    .div-spacer { display: block !important; }

    #domain-box, #idn-box { width: 670px; margin: 0 auto -56px auto; }

    .domain-sbox
    {  display: inline-block;  float: left;  max-width: 324px; width: 324px;  }

    .tld-box, .idomain-box .tld-box
    {  display: inline-block;  float: right; width: 318px; max-width: 348px;  }

    .placeholder-wrapper { width: 322px; }

    .placeholder { border: 1px solid #ddd; border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px; height: 145px; }

    .sd-tarea { border: 0; }

    .row-gray h2.ui-heading, h2.ui-heading span.lite-green, .row-white h2.ui-heading
    {
        margin: 24px auto 0 auto; text-align: center;
        font-family: 'Open Sans', sans-serif; font-size: 26px; line-height: 32px; width:auto;
        font-weight: bold;
    }

    h2.ui-heading span.lite-green { color:#6fc61e; }

    .hastab.tabs-wrapper, .hastab.tabs-wrapper .tab
    { width: 330px; }

    .tabs-wrapper .tab li
    { padding: 12px 0 10px 0; text-align: center; width: 163px; }

    .tabs-wrapper .tab li a, .placeholder-txt1
    { font-size: 18px; font-weight: 500; }

    .tabs-wrapper .tab li.active a { color:#0084e0; }

    .tabs-wrapper .tab li#tab-for-dbox { margin: 0; width: 162px; }

    .tabs-wrapper .tab li.active:after { left: 72px; }

    #ndomins-list, #txt_idn_domain_name, .placeholder-txt2 { width: 280px; }

    #idn-domain-box .placeholder-txt1 { font-size: 17px; }

    #highlight { margin: 0 0 -17px 0; }

    .placeholder-txt2, .domain-action .alink, .dataTable2 a { font-size: 14px; }

    .tld-box.rfloat .list-heading{font-size: 18px; font-weight: 500; }

    .dataTable2 label { font-size: 15px; }

    .domain-action .alink, .dataTable2 a{ color:#377ce4 }

    input[name="Checkout"].uiButton
    { background: #0893d8; border: 1px solid #044bbc;
      border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; width: 222px; height: 37px;
    }

    .feature-blurb ul{ width: 50%; }

    .feature-blurb ul li,
    .feature-blurb ul li .title,
    .feature-blurb ul li .list-info,
    .feature-blurb ul li.ic-8
    { border: 0; }

    .feature-blurb ul li .list-info
    {  display: block; font-size:15px; line-height:21px; margin: 5px 0 0 0; padding: 0 0 0 68px; width: 80%; }

    p.fea-heading{ text-align: center; font-size: 15px; }

    .feature-blurb ul li .title
    { font-size: 18px; font-weight: 600; line-height: 22px; min-height: 0; position: relative; padding: 0 0 0 68px; }

    .feature-blurb ul li .title .feat-icon
    { margin:0 15px 0 0; position: absolute; left:0; top:0; vertical-align: top; height: 64px; width: 53px; }

    .feature-blurb ul li.ic-1 .feat-icon
    {  background: url("/getImage.php?src=ic-fb-1.gif") right 0 no-repeat; }

    .feature-blurb ul li.ic-3 .feat-icon
    { background: url("/getImage.php?src=ic-fb-3.gif") right 0 no-repeat;  }

    .feature-blurb ul li.ic-4 .feat-icon
    { background: url("/getImage.php?src=ic-fb-4.gif") right 2px no-repeat; }

    .feature-blurb ul li.ic-5 .feat-icon
    { background: url("/getImage.php?src=ic-fb-5.gif") right 0 no-repeat; }

    .feature-blurb ul li.ic-6 .feat-icon
    { background: url("/getImage.php?src=ic-fb-6.gif") right 0 no-repeat; }

    .feature-blurb ul li.ic-7 .feat-icon
    { background: url("/getImage.php?src=ic-fb-7.gif") right 0 no-repeat; }

    .feature-blurb ul li.ic-8 .feat-icon
    { background: url("/getImage.php?src=ic-fb-8.gif") right 0 no-repeat; }

    .row-white h2.ui-heading
    { margin: 0 0 7px 0; padding: 0; }

    .feature-blurb ul li.ic-1, .feature-blurb ul li.ic-4
    {  min-height: 132px;  margin: 0 0 35px 0; }

    .feature-blurb ul li.ic-3, .feature-blurb ul li.ic-6 { margin: 0 0 32px 0; }

    .feature-blurb ul li.ic-5, .feature-blurb ul li.ic-8 { min-height: 122px; margin: 0 0 20px 0; }


    /* DCA page */

    .secondary-section .filters { display: inline-block !important; }

    .dca-page-wrapper { width: 100%; margin: 32px auto -5px auto; }

    .dca-search-result-content
    {  position: relative;  margin: 0 auto; float: none; width: 94.8%;  }

    .dca-search
    { padding: 1%;width: 98.1%; }

    .dca-search input
    { width: 77%; padding: 19.5px 0 19.5px 3%;
      color:#222; font-family: 'Open Sans', sans-serif; font-size: 18px; font-weight: 400;
    }

    .dca-search button { width: 19.8%; height: 62px; }

    .primary-result-section, .secondary-result-section
    { width: 99.5%; }

    .secondary-section { width: 100%; }

    .primary-result .dca-domain-name { width: 35% }

    .primary-result .dca-domain-name span.dca-primary-dn { width: 65%; }

    .primary-result .dca-domain-avail { width: 80%; }

    .secondary-result a.tooltipanimated span.classic-ani
    { display: none; }

    .dca-domain-name span.dca-primary-dn span.namevalue, .secondary-section-heading
    { font-family: 'Open Sans', sans-serif; font-size: 18px; font-weight: 600; }

    .dca-domain-avail span .dca-duration { font-size: 14px; }

    .secondary-result { padding: 25px 0;  }

    .secondary-result .dca-domain-name, .secondary-result .dca-pricing { font-size: 16px; height: auto; }

    .secondary-result .dca-domain-avail {height: auto; top:18px; }


    /* DCA page - cart */

    .dca-page-wrapper .cart, .dca-page-wrapper .cart .ppp-modal { background: #f2f2f2 !important; }

    .cart.rfloat {  width: 94.5%;  left: 50%;  margin-left: -47.4%;  }

    .dca-page-wrapper .cart .ppp-modal, .cart-list
    { width: 55%;  margin: 0 0 0 2.5%; display: inline-block; border-right: 1px solid #ccc; }

    .dca-page-wrapper .cart-list { margin: 2% 0 0 2.5%; padding: 0; }

    .dca-page-wrapper .cart .ppp-modal { margin: 0 0 2% 2.5%; padding: 12px 0px 0 0; border-top: 0; }

    .dca-page-wrapper .cart-header h3 { padding-left: 2.5%; }

    .dca-page-wrapper .cart-header
    { background: #e0e0e0; color: #1b1b1b; font-family: 'Open Sans', sans-serif; font-size: 16px; font-weight: 600;  }

    .ppp-tobe-enabled p, .ppp-tobe-enabled a
    { display: inline-block; }

    .dca-page-wrapper .cart-list li{ padding: 0 20px 17px 0; }

    .dca-page-wrapper .total-container
    { display: inline-block;  margin: -20px 0 0 0; vertical-align: top;  width: 40%;  }

    .dca-page-wrapper .cart .ppp-modal
    { padding: 12px 0px 2% 0; }

    .total-container.collapsed { margin: 20px 0 20px 20px; width:40%; padding: 0;  }

    .total-container.expand
    {  margin: -20px 0 0 20px;  width: 23%;  padding: 0;  }

    span.dca-domain-purchased { width: 95%; }

    .dca-page-wrapper .cart .ppp-modal .enable-ppp-arrow
    { background: url("/getImage.php?src=pp_modal_top_seperator.png") no-repeat; width: 393px; height: 10px; left: 0; }

    .dca-page-wrapper .total-container span#total
    { font-size: 20px; }

    /* IDN error handling  */

    .tld-box .cursive
    { top: 90px;  left: 363px  }

}





/* Mobile */
@media only screen and (min-width: 320px) and (max-width: 767px)
{

    .wrp.bg-banner, .wrp
    { margin: 0 auto; width: 87.5% !important; }

    .sslc-banner .content-wrp, .sslc-banner h2
    { text-align: center; width: 100%; }

    .sslc-banner p,
    .title2:before, .title2:after,
    .sslc-plans .div-spacer,
    .sc-plans .price
    { display: none; }

    .title2 { font: 20px/26px 'Open Sans', sans-serif; padding: 0;  }

    /* Banner */


    .sslc-banner .bg-banner { min-height: 335px;  }

    .sslc-banner { padding:0; }

    .sslc-banner .bg-banner { background: url("/getImage.php?src=ssl-bg-banner-mobile.png") no-repeat center bottom -9px; }

    .sslc-banner h1 { padding: 30px 0 0 0; display: block; text-align: center; }

    .sslc-banner h1 img { height: 30px; width: 130px; }

    .sslc-banner h2 {  font: bold 20px/26px 'Open Sans', sans-serif; margin: -5px 0 0 0; }

    .sslc-price-row { padding: 13px 0 20px 0; }

    .sslc-price-row	h3 {  font: 600 20px 'Open Sans', sans-serif; }

    .sslc-price-row .price {  font: 600 23px 'Open Sans', sans-serif;  }

    .sslc-price-row .currency{  font: 400 14px 'Open Sans', sans-serif;  }

    .sslc-price-row .duration {font:  400 15px 'Open Sans', sans-serif; }

    .sslc-price-row .lfloat, .sslc-price-row .rfloat
    { float: none; display: block; text-align: center; margin: 0 auto; padding: 0; width: 87.5%; }

    .sslc-price-row .rfloat {  font: 400 15px 'Open Sans', sans-serif; margin: 13px auto 0 auto; padding: 10px 0; }

    .sslc-plans .title2 { margin: 0 auto 18px auto; }


    /* Feature */

    .sslc-features .title2 { margin: 30px auto 25px auto; }

    .features-block
    { width: 100%; }

    .features-block p { text-align: left; }

    .features-block .block1, .features-block .block2, .features-block .block3,
    .features-block .block4, .features-block .block5
    { background-position: left top; min-height: 0; padding: 0 0 0 60px;  text-align: left; width: 80%; }

    .features-block .block1 { background-size: 42px 50px; }

    .features-block .block2 { background-size: 42px 46px; }

    .features-block .block3 { background-size: 44px 45px; }

    .features-block .block4 { background-size: 44px 54px; }

    .features-block .block5 { background-size: 49px 37px; }

    .features-block h4 {  font: 600 15px 'Open Sans', sans-serif; }

    .features-block p {  font: 400 14px/21px 'Open Sans', sans-serif; margin: 4px 0 0 0;  padding: 0 0 32px 0; }


    /* Plans */

    .sslc-plans { padding: 0 0 70px 0; }

    .sc-plans,
    .sc-plans .plan-block {  margin: 0; width: 100% }

    .sc-plans .plan-block
    { margin: 0 0 30px 0 }

    .sc-plans h2 {  font: 600 18px/21px 'Open Sans', sans-serif; padding: 10px 0 7px 0; }

    .sc-plans .feature li, .sc-plans .ui-button2, .sc-plans .plan-duration, .sc-plans .spacer
    {  font: 400 14px 'Open Sans', sans-serif; width: 85%; margin: 0 auto; }

    .sc-plans .plan-duration
    { -moz-appearance: none; -webkit-appearance: none; appearance: none;
        background: url('/getImage.php?src=sh-hosting-plans-dd.png') right center no-repeat #fff;
        border-radius: 0; -moz-border-radius: 0; -webkit-border-radius: 0;
        padding: 4.5px 0 4.5px 8px; margin: 18px 0 0 0;
    }

    .sc-plans .ui-button2
    { margin: 20px 0 0 0; padding: 10px 0; }

    .sc-plans .feature { margin: 15px 0 13px 0; }

    .sc-plans .feature li:before
    { border:1px solid #c33c29; content: "";
      border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px;
      font: 400 14px 'Open Sans', sans-serif;
      display: inline-block; height: 6px; margin: 3px 5px 0 0; vertical-align: top; width: 6px;  }

    .sc-plans .feature li { text-align: left; padding: 6px 0; }


    /* FAQ's */

    .feq-wrp .ssl-tabs {  display: block; width: 100%; text-align: left;  margin: 13px auto 0 auto;  }

    .feq-wrp .ssl-tabs a.active, .feq-wrp .tab-content-wrp { border: 0; }

    .feq-wrp, .feq-wrp .tab-content-wrp { padding: 0; width: 100%; }

    .feq-wrp .tab-content-wrp { padding: 25px 0 0 0; }

    .tab-content-wrp { margin: 0; }

    .feq-wrp .ssl-tabs a { padding: 0; position: static; }

    .feq-wrp { border-bottom:1px solid #ddd; position: relative; }

    .feq-wrp .que
    { border-top:1px solid #ddd; cursor: pointer; font: 400 14px/21px 'Open Sans', sans-serif; padding:7px 40px 6px 0; position: relative; }

    .feq-wrp .ans
    { border-bottom:1px solid #ddd;  display: none; font: 400 14px/18px 'Open Sans', sans-serif;
      margin: 0 0 -1px 0; padding: 0 0 15px 15px;
    }
    .faq-cont.tab-cont .que .plus
    { background: url("/getImage.php?src=ssl-faqs-plus.png") no-repeat; cursor: pointer; display: block; position: absolute;
        height: 19px; top: 50%;  transform: translate(0,-50%); right: 0; width: 19px;
    }

    .faq-cont.tab-cont .que .plus.minus
    { background-position: 0 -9px; height:1px; top: 50%;  transform: translate(0,-50%); width: 19px;   }


    /* Tabs */

    .sslc-banner { position: relative; }

    .tabs-txt
    {  height: 35px; background: #f8f8f8; border-bottom: 1px solid #f0f0f0;  width: 100%;  position: absolute; bottom:-35px; z-index: 99; }

    .tabs-txt span
    {  font: 400 13px/22px 'Open Sans', sans-serif; color: #000; text-transform: uppercase; line-height: 35px; display: inline-block; }

    .tabs-txt-content { width: 100%; margin: 0 auto; text-align: center; }

    .tabs-txt span { padding: 0 25px; }

    .tabs-txt span:first-child { float: left; }

    .tabs-txt span:last-child { float: right; text-align: right; }

    .tabs-txt span.clicked { border-bottom: 2px solid #bf2e1a; }

    /* Extra View Plans Button */

    .blue-view-plans
    {  background: #0893d8; border: 1px solid #044bbc; display: block;
       font: 400 15px 'Open Sans', sans-serif; color: #fff;
       border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px;
       padding: 10px 0; text-align: center;    width: 100%;  position: absolute;  bottom: -70px;
    }

    #select-domain-wrapper { width: 100% }

    #select-domain-modal
    { width: 87.5%; margin: 0 0 0 -43.75%; }

    #select-domain-modal .inner-content div.lfloat,
    #select-domain-modal .inner-content .purchase-sitelock.lfloat
    { width: 100%; margin: 0; padding: 0; }

    #select-domain-modal .inner-content .purchase-codeguard.lfloat
    { margin-top: 20px; }

    #select-domain-modal .register-new input[type="text"],
    #select-domain-modal .use-existing input[type="text"]
    {
        width: 87.5%;
    }

}


/* Tablet */
@media only screen and (min-width: 768px) and (max-width: 1000px)
{

    /* Banner */

    .sslc-banner
    { padding:40px 0 0 0; width: 100%; }

    .wrp.bg-banner, .wrp
    { margin: 0 auto; width: 95.5% !important; }

    .sslc-banner .bg-banner
    { background: url(/getImage.php?src=ssl-bg-banner-tab.png) no-repeat right -10px bottom -45px; }

    .sslc-banner h1 { padding: 10px 0 0 0; }

    .sslc-banner h2
    { font:bold 26px/32px 'Open Sans', sans-serif; padding: 3px 0 10px; width: 80%; }

    .sslc-banner p
    { font: 400 15px/22px 'Open Sans', sans-serif; margin: 7px 0 0 0; padding-bottom: 43px; width: 390px; }

    .sslc-banner .content-wrp img { height: 34px; width: 144px; }

    .sslc-price-row {
        padding: 20px 0; }
    
    .sslc-price-row	h3
    { font: 400 24px 'Open Sans', sans-serif; }

    .sslc-price-row .price {  font: 400 30px 'Open Sans', sans-serif;  }

    .sslc-price-row .currency, .sslc-price-row .duration
    { font: 400 18px 'Open Sans', sans-serif; }

    .sslc-price-row .ui-button2
    { font: 400 18px 'Open Sans', sans-serif; }


    /* Features */

    .features-block { width: 100%; }

    .features-block p {  font: 400 14px/21px 'Open Sans', sans-serif; padding: 0;  }

    .features-block h4 {    font: 600 16px/22px 'Open Sans', sans-serif; }

    .title2 { font: 600 22px/32px 'Open Sans', sans-serif; margin: 15px 0 18px 0; padding: 0;  }

    .title2:before, .title2:after { display: none; }

    .sslc-features{ margin: 0 0 20px 0; }

    .blurb-fea.block1, .blurb-fea.block2, .blurb-fea.block3, .blurb-fea.block4, .blurb-fea.block5
    { min-height: 0; }

    .blurb-fea.block1, .blurb-fea.block2, .blurb-fea.block3
    { padding: 76px 33px 0 0; width: 210px; }

    .blurb-fea.block3 { padding-right: 0;  width: 240px; }

    .blurb-fea.block4, .blurb-fea.block5
    { padding: 80px 45px 0 0; margin: 33px 0 0 0; width: 225px;   }


    /* Plans */

    .sc-plans { margin: 0 auto -75px auto; width: 75%; }

    .sc-plans .plan-block { width: 31%; }

    .plan-block.plan3 { margin: 0; }

    .sc-plans h2 {  font: 600 16px 'Open Sans', sans-serif; padding: 10px 0; }

    .sc-plans .price {  font: 400 24px 'Open Sans', sans-serif; padding: 15px 0; }

    .sc-plans .duration, .sc-plans .currency {  font: 400 14px 'Open Sans', sans-serif;  }

    .sc-plans .feature li, .sc-plans .ui-button2, .sc-plans .plan-duration, .sc-plans .spacer
    {  font: 400 14px 'Open Sans', sans-serif; width: 92%; margin: 0 auto; }

    .sc-plans .plan-duration
    { -moz-appearance: none; -webkit-appearance: none; appearance: none;
      background: url('/getImage.php?src=sh-hosting-plans-dd.png') right center no-repeat #fff;
      border-radius: 0; -moz-border-radius: 0; -webkit-border-radius: 0;
      padding: 4.5px 0 4.5px 8px;
    }

    .sc-plans .ui-button2
    { margin: 20px 0 0 0; padding: 10px 0; }

    .sc-plans .spacer {  margin: 15px auto 23px auto;  }

    .sc-plans .feature li { padding: 6px 0; }

    /* FAQ's */

    .feq-wrp .ssl-tabs a.active, .feq-wrp .tab-content-wrp { border: 0; }

    .feq-wrp { margin: 40px auto 0 auto; }

    .feq-wrp .tab-content-wrp { margin: 0; padding: 0; }

    .feq-wrp .ssl-tabs a { display: block; padding: 0 0 28px 0; text-align: center;  }

    .faq-cont.tab-cont .que, .faq-cont.tab-cont .ans{ border: 1px solid #ddd; margin: -1px 0 0 0;  }

    .feq-wrp .ans { padding: 15px 20px 10px 20px; font: 400 14px/21px 'Open Sans', sans-serif; display: none; }

    .faq-cont.tab-cont .que { cursor: pointer; padding: 15px 20px; position: relative; }

    .faq-cont.tab-cont .que .plus
    { background: url("/getImage.php?src=ssl-faqs-plus.png") no-repeat; cursor: pointer; display: block; position: absolute;
      height: 19px; top:16px; right: 20px; width: 19px;
    }

    .faq-cont.tab-cont .que .plus.minus
    { background-position: 0 -9px; height:1px; top:25px; width: 19px;   }

    /* Select Domain Modal */

    #select-domain-modal { margin-left: -345px; }

}