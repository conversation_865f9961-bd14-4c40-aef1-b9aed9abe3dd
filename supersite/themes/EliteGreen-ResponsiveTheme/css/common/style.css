/* Global */
@import url('https://fonts.googleapis.com/css2?family=Rokkitt:wght@400;700&display=swap');
body {font:normal 12px Arial, Helvetica, sans-serif; color:#434343; margin:0px; padding:0px; background:#D6D6D6;}
ul li {list-style-type:none;}
h1 {color:#454545; font-size:24px;}
h4 {font:bold 18px Arial,Helvetica,sans-serif; color: #434343; margin: 0; padding: 0 0 10px 0;}
.clear {clear:both;}
.comman-list ul {margin-left:20px;}
.comman-list li {list-style:disc; font-size:12px; margin:10px 0;}
/* Body style */
#body-wrapper {background:#FFF;}
.main-content-wrapper {padding: 0 40px 20px;}

/* Global Ends */

.error {
	background:none;
	border:1px solid #FF8080;
	color:#333333;
	font-size:12px;
	font-weight:bold;
	line-height:1.2em !important;
	margin:10px 0;
	padding:10px 0 8px 28px;
	text-align:left;}
/* Page style */
.row-gray {
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #ffffff), color-stop(1, #f3f3f3) );
	background:-moz-linear-gradient( center top, #ffffff 5%, #f3f3f3 100% );
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#f3f3f3');
	background-image:-ms-linear-gradient(top, #fff 0%, #f3f3f3 100%);	
	background-image:linear-gradient(top, #fff 0%, #f3f3f3 100%);
	background-color:#f3f3f3;
	border-bottom:solid 1px #e8e8e8;
	padding-bottom:5px;}
.row-white {background:#fff;}
.row-indent {padding-left:30px;padding-right:30px;}
/* Page Ends */

/* Head style */
#head-wrapper {height: 85px; margin: 0 auto; width: 960px;}
#logo {float: left; padding: 10px 0 0;}
#help-line {
	float: right;
	width:270px;
	margin: 3px 5px 0 0;
	padding:6px 0 1px 0;
	}
#help-line h3 {
	color:#ec6f3e;
	font-size:18px;
	padding: 6px 0 1px 23px;
	font-family:Arial, Helvetica, sans-serif;}
#help-line p {line-height: 21px; margin: 0; padding: 0 0 0 23px;}
#help-line .live-but { float:left; width:70px; height:23px; margin-top:4px;}
#help-line .live-but a {
	
	color:#ffffff;
	font-size:12px;
	font-family: arial;
	font-weight:normal;
	padding: 3px 0;
	text-align: center;
	display:block;
	text-decoration:none;}
#help-line .live-but a:hover {color:#424242;}
#help-line .contact-but, #help-line .support-but {
	float:left;
	width:70px;
	height:23px;
	margin-left:5px;
	margin-top:4px;}
#help-line .contact-but a, #help-line .support-but a {
	background:url(/getImage.php?src=contact-but-bg-live-help.gif) no-repeat;
	color:#ffffff;
	font-size:12px;
	font-weight:normal;
	font-family: arial;
	padding:3px 0px;
	display:block;
	text-decoration:none;
	text-align:center;}
#help-line .contact-but a:hover, #help-line .support-but a:hover {color:#000000;}
#dashboard {float: right; background: url(/getImage.php?src=dashboard-courv-top.gif) no-repeat 0px 0px; width:284px; height: 70px; position:relative;}
#dashboard_user {
	font-size: 14px;
	font-weight: bold;
	color:#616161;
	margin:0px;
	padding:0;
	position:absolute;
	top:8px;
	left: 13px;}
#signout_link {font-weight:normal; font-size:11px; color:#666; text-decoration: underline;}
#cart_bottom {
	position:absolute;
	bottom:0;
	left:4px;
	width:276px;
	height:9px;
	display:block;
	overflow:hidden;
	background: url(/getImage.php?src=dashboard-courv-bottm.gif) #dfdfdf repeat 0 0;}
#dashboard-myAccountLink {position:absolute; top:11px; right:15px; text-decoration:underline;}
#dashboard a.dashboard-checkoutLink, #dashboard a.dashboard-checkoutLink-disabled {
	background:url(/getImage.php?src=bg-checkout-button.png) no-repeat left top;
	width:102px;
	height:25px;
	line-height:25px;
	text-align:center;
	display:block;
	font-size:12px;
	text-decoration:none;
	color:#d26c1a;
	text-shadow:1px 1px 1px #fff;
	cursor:pointer;
	position:absolute;
	bottom:8px;
	right:13px;}
#dashboard a.dashboard-checkoutLink:hover {color:#F7862B;}
#dashboard a.dashboard-checkoutLink-disabled {background:url(/getImage.php?src=bg-checkout-button.png) no-repeat left -25px; color:#999;}
#dashboard .login-txt {display: none;}
#dashboard .login-txt a {
	padding-top:5px;
	font-size:12px;
	color:#005CBD;
	font-weight:normal;
	float:right;
	text-decoration:underline;}
#dashboard .cart-items-wrapper {
	bottom: 10px;
	left: 13px;
	margin: 0;
	line-height: 18px;
	padding: 0 0 0 21px;
	position: absolute;
	height:18px;
	overflow:hidden;
	background:url(/getImage.php?src=cart.gif) no-repeat 0px 0px;}
#dashboard .cart-items-wrapper a:link, #dashboard .cart-items-wrapper a:visited {color:#666; text-decoration:none;}
#dashboard .cart-items-wrapper span.fauxLink {text-decoration:underline;}
.cartbar-cartItemCount {background:#F7862B;}
#dashboard .cart-items-wrapper a span.noItems {background:#D5D5D5;}
#dashboard .cart-items-wrapper a span.hasItems {background:#f7862b;}
#dashboard .login-txt a:hover {text-decoration:none;}
#dashboard .account {float:right; padding-top:3px; font-size:12px;}
#dashboard .account a {color:#005CBD; text-decoration:underline;}
#dashboard .account a:hover {text-decoration:none;}
#dashboard .cart-items {float:left; background:url(/getImage.php?src=cart_items.gif) no-repeat 0px 0px;
    width:95px;
	line-height:23px;
	padding-left: 29px;
	color:#ffffff;
	margin-top:2px;}
#dashboard .cart-items a {color:#ffffff; font-weight:normal; font-size:10px;}
#dashboard ul {padding:0px 0px;}
#dashboard li {color:#fff; background:url(/getImage.php?src=gray-but-right.gif) no-repeat right 0px; padding:0px 0px; margin-right:15px; float:left;}
#dashboard li a {
	color:#fff;
	font-size:11px;
	background:url(/getImage.php?src=gray-but-left.gif) no-repeat left 0px;
	padding:5px 8px;
	text-decoration:none;
	display:block;}
#dashboard li a:hover {color:#000;}
/* Head Ends */


/* Menu nav style */
#nav-wrapper {background:url(/getImage.php?src=nav-bg-black.gif) no-repeat left top; width:960px; height:48px;}
/*------------------------------------------------------
										Top menu
------------------------------------------------------*/
.topmenubg {margin:0 auto; width:930px; padding:0;}
.topmenu {margin:0; padding:0;}
#dropmenu {margin:0; padding:0; width:100%;}
#dropmenu, #dropmenu ul {
	margin:0;
	padding:0;
	list-style-type:none;
	list-style-position:outside;
	position:relative;
	z-index:400;
	width:100%;}
#dropmenu a {
	font-family: arial;
	display:block;
	padding:16px 19px 15px 19px;
	color:#ffffff;
	background:url(/getImage.php?src=nav-bg.gif) repeat-x;
	text-decoration:none;
	font-size:13px;
	font-weight:bold;
	border-right:
	1px solid #627b93;}
#dropmenu a:hover {color:#ffffff; background:url(/getImage.php?src=nav-bg-over.gif) #314354 repeat-x;}
#dropmenu li {float:left; position:relative; padding:0; margin:0;}
#dropmenu li a:hover {color:#ffffff; background:url(/getImage.php?src=dropmenu-over-bg.gif) repeat-x;}
#dropmenu ul {
	position:absolute;
	display:none;
	width:16.8em;
	top:45px;
	left:-1px;}
#dropmenu ul a {padding:0.60em 0;}
#dropmenu li ul {background:#314354;}
#dropmenu li ul li a {
	width:15em;
	height:auto;
	padding-left:19px;
	float:left;
	border-right: 0px solid #314354;
	border-bottom:1px solid #2d3d4d;
	border-top:1px solid #435363;
	font-size:12px;
	background:url(/getImage.php?src=sqr-white.gif) #314354 no-repeat 10px 12px;
	color:#fff;}
#dropmenu li ul li a:hover {color: #c5def4; background:url(/getImage.php?src=sqr-white-pver.gif) #253542 no-repeat 10px 12px; border-right: 1px solid #314354;}
#dropmenu ul ul {top:auto;}
#dropmenu li ul ul {left:15em; margin:0px 0 0 10px;}
#dropmenu li:hover ul ul, #dropmenu li:hover ul ul ul, #dropmenu li:hover ul ul ul ul {display:none;}
#dropmenu li:hover ul, #dropmenu li li:hover ul, #dropmenu li li li:hover ul, #dropmenu li li li li:hover ul {display:block;}
#dropmenu li.current_page_item a {background:url(/getImage.php?src=nav-bg-over.gif) #314354 repeat-x; color:#b6cee4;}
#dropmenu li.current_page_item li a {background:url(/getImage.php?src=sqr-white.gif) #314354 no-repeat 10px 12px; color:#ffffff;}
#dropmenu li.current_page_parent a {color:#fff;}
#dropmenu li.current-cat a {background:#fff; color:#c5def4;}
/*#primaryDomain .duration {position:relative; top:-28px;}*/
#dropmenu li.home_start a {background:url(/getImage.php?src=nav-home-bg.gif) no-repeat left top; padding:1.25em 1.5em;}
#dropmenu li.home_start a:hover {background:url(/getImage.php?src=nav-home-bg-over.gif) no-repeat left top;}
#dropmenu li.reseller_end a {background:url(/getImage.php?src=nav-reseller-bg.gif) no-repeat right top; border-right:0px; padding:1.25em 1.59em;}
#dropmenu li.reseller_end a:hover {background:url(/getImage.php?src=nav-reseller-bg-over.gif) no-repeat right top;}
#dropmenu .lavaLamp li.back {background: url(/getImage.php?src=lava.gif) no-repeat right -30px; width: 9px; height: 30px; z-index: 8;position: absolute;}
#dropmenu .lavaLamp li.back .left {background: url(/getImage.php?src=lava.gif) no-repeat top left; height: 30px; margin-right: 9px;}
/* Menu nav Ends */

/* /Main Content style main blurb, right left column */
#content-wrapper {padding:0px; width:960px; margin: 0 auto;}
.left-column {float:left; width:525px; padding-top:15px;}
.right-column {float:right; width:435px; padding-top:15px;}
.wide-left-column {float:left; width:622px;	padding-top:15px;}
.narrow-right-column {float:right; width:338px; padding-top:15px; display:inline;}
/* /Main Content style main blurb, right left column End */

/* Check Domain blurb */
.check-domain-blurb {height:311px; background: url(/getImage.php?src=domains-blurb-background.jpg) #ffffff no-repeat left top;}
.search-domain {padding: 6px 10px 0 20px}
.search-domain h1 {
	font-size:28px;
	color:#fff;
	line-height:20px;
	margin:0px;
	padding:15px 0px 10px 0;}
.domain-search-box {height:38px;}
.domainSearchResult .domain-search-box {margin:0 0 25px 8px;}
.domains-input {font-size: 18px; width:270px; padding: 1px 3px; float:left;}
.domains-submit {
	background: url(/getImage.php?src=domain-search-go-but.gif) #6cbff2 no-repeat left 0px;
	border:none;
	color:#233d4d;
	font-size:15px;
	width:45px;
	height:41px;
	float:left;
	margin-right: 50px;}
.domains-www {
	color:#a4a4a4;
	font-size:19px;
	float:left;
	padding-top: 5px;
	padding-left: 12px;}
.domains-select {color:6a6d6f; font-size:19px; float:left; position: relative;}
.domains-select .droupdown-tld {
	/*background: url(/getImage.php?src=tld-dropdown.gif) no-repeat left 0px;*/
	z-index:10;
	width:25px;
	height:34px;
	position:absolute;
	left:88px;}
.search-note {width:400px; margin:0px; padding:0px;}
.search-note .sd-note {
	color: #a4a4a4;
	font-size:11px;
	padding-top: 3px;
	padding-left:5px;
	float:left;}
.search-note .bulk-links a {
	color: #005cbd;
	font-size:11px;
	padding:0px 10px;
	padding-top: 4px;
	font-style:normal;
	text-decoration:underline;
	float:right;}
.search-note .bulk-links a:hover {text-decoration:none;}
.free-list, .price-list, .answerable-mask {float:left; padding:0px; margin-top:30px;}
.free-list {border-left: 1px solid #ffffff; padding-left: 15px; width:190px;}
.price-list {padding-right: 10px; width:275px;}
.answerable-mask {margin-top:5px; padding-left: 20px;}
.free-list h5, .price-list h5 {color: #434343; font-size:16px; margin:0px; font-weight:bold; padding: 1px 0 5px 0;}
.free-list h5 span, .price-list h5 span {color:#ec6f3e;}
.free-list ul, .price-list ul {margin:0px; padding:0px;}
.free-list li, .price-list li {
	color: #696969;
	font-size:12px;
	font-weight:normal;
	background:none;
	padding:0;}
.free-list ul {width:180px;}
.free-list li {background: url(/getImage.php?src=green-check.gif) no-repeat 0px 5px; padding: 3px 0px 3px 22px; color:#696969;}
.price-list ul {float:left;}
.price-list ul li {
	color:#696969;
	font-size:12px;
	font-weight:normal;
	line-height:18px;
	clear:both;}
.price-list ul li div.col1 {float:left; font-weight:bold; width:125px;}
.price-list ul li div.col2 {float:left; font-weight:bold; width:75px;}
.price-list ul li div.col3 {
	float:left;
	font-weight:bold;
	font-size:14px;
	color:#70a750;
	width:75px;}
/* Check Domain blurb */

/* --------------------- CART TOTALS AREA --------------------- */
td.CartTotal {padding-top:10px;}
.CartTotal p {
	font:bold 18px Arial, Helvetica, sans-serif;
	color:#727272;
	line-height:20px;
	margin:0;
	padding:0;
	text-align:right;}
.CartTotal .ItemTotal p {
	font-size:18px;
	line-height:20px;
	font-weight:bold;
	color:#333;
	margin:0px 0 0;
	padding:0;
	text-align:right;}
#CartTotal, #taxTotalCurrency {padding-left:10px; text-align:left;}
#CartTotal #total {font-size:20px;}
td.itemAmount {padding-left:15px;}
/* --------------------- Coupon Discount --------------------- */

p.ItemDiscTotal {color: #70A750;}
.ItemConvertedSubtotal {font-size:14px;}
.ItemConvertedSubtotal #CartTotal, .ItemConvertedSubtotal #CartTotal #total {font:bold 18px Arial, Helvetica, sans-serif; color:#727272;}
.Cart-Total table td {border:0;}
.Cart-Total {
	background:#f4f4f4;
	padding:15px 25px;
	margin:5px 0 5px 5px;
	border-radius:8px;
	-moz-border-radius:8px;
	-webkit-border-radius:8px;
	font:bold 25px Arial, Helvetica, sans-serif;
	color:#6f6f6f;
	border:solid 1px #e5e5e5;}
.Cart-Total .cart-item{ font:bold 25px Arial, Helvetica, sans-serif;}
.ItemDiscountTotal p {font-size:12px  color: #70A750}
.ItemTotalAfterDiscount td {margin-top:5px  padding-top:5px; padding-bottom:5px; border-top:1px solid #c1c58c;}
.ItemTotalAfterDiscount td p {font-size:24px; font-weight:bold; padding-left: 15px;}
.ItemTotalAfterDiscount #TotalAmount {color: #70A750}
/* --------------------- Tax Handling --------------------- */

.taxTotal {font-size:14px !important; color: #666 !important;}
/*_________ Cart _________*/

#login {margin-bottom:70px;}
.CartSection {
	clear:both;
	font-family:Arial, Helvetica, sans-serif;
	text-align:left;
	color:#555;
	padding-bottom:10px;}
.CartSection h2.HeadingInactive {background: 0 -5px url(/getImage.php?src=bg_cart_headings.png) no-repeat; color:#ACACAC;}
.CartSection h2.HeadingActive {background: bottom left url(/getImage.php?src=bg_cart_headings.png) no-repeat; color:#2d5494; margin-top:10px;}
.CartSection h3 {font:bold 28px 'Rokkitt', serif; padding:0 0 5px 0; color:#585858;}
.CartSection .hint {
	font-size:12px;
	line-height:20px;
	margin:0 0 0 0;
	padding:0;
	color:#777;}
.strikethrough-text {text-decoration: line-through;}
.CartSection div.includedFree {padding-left:0; margin-top:5px; width:370px;}
.CartSection .showmore {display:block;}
.CartSection .showmore .FreeItemCart {display: none;}
.CartSection .quickSummary {display: none;}
/* TABLE STYLES*/

#CartTable, #CartTableFooter {border:0;	color:#666;}
#CartTable th {font:bold 20px Arial, Helvetica, sans-serif; color:#0f0f0f; background:#ededed; border-bottom:solid 2px #fff;}
#CartTable td, #CartTable th {padding:10px 12px; vertical-align:top;}
#CartTable .cartname {font:normal 16px Arial, Helvetica, sans-serif; color:#575757;}
#CartTable tr.CartItemRow td {border-bottom:1px solid #ddd;}
#CartTable tr.CartItemRow td .invoiced {background:url("/getImage.php?src=ico-info.gif") no-repeat scroll 0 8px transparent; padding: 7px 0 4px 22px; cursor: help; display: inline-block; *display: inline;}
#CartTable .CartDuration, #CartTable .CartSubTotal {vertical-align: top; font:normal 16px Arial, Helvetica, sans-serif;}
#CartTable .CartSubTotal {width:auto;}
#CartTableFooter td {vertical-align:top;}
#CartTableFooter .ItemConvertedSubtotal td {font:bold 18px Arial, Helvetica, sans-serif; color:#727272;}
.CartItem .ItemTitle {
	font-size:18px;
	line-height:20px;
	font-weight:bold;
	color:#666;
	margin:10px 0 0;
	padding:0;
	width:auto;
	word-wrap:break-word;
	overflow:hidden;}
.CartItem .ItemSubTitle {
	font-size:12px;
	line-height:20px;
	color:#777;
	margin:0px 0 0;
	padding:0;}
.CartItem:hover .ItemTitle, .CartItem:hover .ItemSubTitle {color:#000;}
#CartTable .even td {background:#fff;}
#CartTable .odd td {background:#f6f6f6;}
#CartTable .showless a, #CartTable .showmore a {font:normal 13px Arial, Helvetica, sans-serif;}
.item-close a {
	background:url(/getImage.php?src=close.png) no-repeat;
	display:inline-block;
	text-indent:-999em;
	height:14px;
	width:14px;}
#CartTableFooter {margin:20px 0;}
#form_couponForm #input_coupon_code {
	font:normal 16px Arial, Helvetica, sans-serif;
	padding:5px;
	box-shadow:1px 1px 2px #f0f0f0 inset;
	border:solid 1px #d0d0d0;
	border-radius:5px;
	-moz-moz-border-radius:5px;
	-webkit-border-radius:5px;
	margin:10px 0;}
#couponCodeContainer .txtblue {font:bold 18px Arial, Helvetica, sans-serif; color:#2265ac;}
.CartSubTotal p {
	font-size:16px;
	line-height:20px;
	color:#575757;
	margin:0;
	padding:0;}
.CouponCode p {padding:0; font-size:12px;}
.CouponCode p a {text-decoration:underline;}
.CartTotal span#CartTotal #total {color:#4d9d1e; font-size:20px;}
.CartTotal span#CartTotal {color: #4D9D1E; padding-left:10px;}
td.CartTotal {padding-top:10px;}
.CartTotal .itemText {font:bold 18px Arial, Helvetica, sans-serif; color:#727272;}
td.CartTotal tr td {padding:3px 0;}
td.CartTotal tr.taxTotal td {padding:3px 0 6px;}
td.CartTotal tr.taxTotal td p a {font-size: 13px; padding: 0 2px;}
.CartDuration select {
	width: 200px;
	height: 26px;
	text-align: left;
	font-family: 'Arial';
	font-size: 13px;
	color: #555;
	font-style: normal;
	font-weight: normal;
	text-decoration: none;
	border:1px solid #CCCCCC;
	padding:3px;}
.CartSection .ItemTitle {
	color:#575757;
	font:normal 16px Arial, Helvetica, sans-serif;
	line-height:20px;
	margin:0;
	padding:0;
	word-wrap:break-word;}
#zeroValueCart {display: none;}

/* -- Coupon Codes Styles -- */

.greenText {color: #70A750; font-weight: bold; display: block;}
#couponCodeContainer {margin:5px 0 0;}

/* Ajax State */
.hasAjax {margin: -10px 0 16px 10px;}
#couponCodeContainer .couponError {margin: 5px 0 0; color: #FF0000; display: none;}
#couponCodeContainer .couponWarning {margin: 10px 0 0; color: #0000FF; display: none;}
#couponCodeContainer #removeCoupon {display: none;}
#couponSuggestions {clear:both; display:none; width:100%; background: #F1F8FF; margin: 10px 0;}
#couponSuggestions h4 {
	color: #3085C8;
	font-size: 12px;
	font-weight: bold;
	margin: 0;
	padding: 7px 5px 6px;
	cursor: pointer;}
#couponSuggestions .toggleDiv {overflow: hidden;}
#couponDomainSelect {font-size: 13px; padding-left: 5px; height: 150px;}
#couponDomainSelect .HeadingInactive {text-align: left; margin: 0; width: 375px;}
#couponDomainSelect select {
	width: 250px;
	height: 26px;
	text-align: left;
	font-family: 'Arial';
	font-size: 13px;
	color: #555;
	font-style: normal;
	font-weight: normal;
	text-decoration: none;
	border:1px solid #CCCCCC;
	padding:3px;
	margin-right: 10px;}
#suggestionList {margin: 0 0 4px; padding: 0 0 0 25px;}
#suggestionList li {list-style: decimal; line-height: 18px;}
#loader {
	width: 99%;
	height: 99%;
	margin: 0.5%;
	padding: 0;
	background: #70A750;
	position: absolute;
	top: 0;
	left: 0;
	opacity: .3;
	filter: alpha(opacity=30);
	-moz-opacity: 0.3;}
#loader_back {
	width: 260px;
	height: 50px;
	left: 330px;
	padding: 10px;
	background: #F9F9F9;
	border: 1px solid #242424;
	position: absolute;
	text-align: center;}
#loader_back #loader_img {background: url("/getImage.php?src=30preloader.gif") no-repeat scroll left top #FFFFFF; height: 15px; width: 125px; margin: 6px auto;}
#shopping-cart {position: relative;}
.invoiced {background:url("/getImage.php?src=ico-info.gif") no-repeat scroll 3px 2px transparent; padding: 4px 0 2px 22px; cursor: help;}
/* Account Summary */

.AccountContact {float:left;}
.AccountAddress {float:left; margin-left:100px;}
#signup_div {display:none;}
.loginform {width:420px; float:left;}
.resellerloginform {width:1000px; float:left; margin-top: -22px;}
#loginForm, #signup_div, .gray-shdow {
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #f3f3f3), color-stop(1, #ffffff) );
	background:-moz-linear-gradient( center top, #f3f3f3 5%, #ffffff 100% );
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f3f3f3', endColorstr='#ffffff');
	background-color:#f3f3f3;
	padding:15px 30px;
	-moz-border-radius:5px;
	-webkit-border-radius:5px;
	-ms-border-radius:5px;
	border-radius:5px;	}
#ExistingUserLogin {margin-right:0;}
#account_summary {padding:0px 0 0 0;}
/* Login Form */

h2 span.floatright, h3 span.floatright {
	float:right;
	font-size:11px;
	font-weight:normal;
	color:#444;
	padding-top:3px;
	padding-right:15px;}
.asterix {color:#F00;}
.loginform p.hint {margin:10px 10px 15px; font-style:italic; color:#777; font-size:12px;}
.loginform div.error {padding:5px 0 3px 28px; margin:5px 15px 15px;}
.loginform div.error li {color:#FF0000;}
.loginform label {font:normal 15px Arial, Helvetica, sans-serif; color:#626262; display:block; margin:5px 0; width:145px;white-space: nowrap;}
.loginform div{padding:0 0 10px 0;}
.loginform input, .loginform select, .loginform textarea {width:290px;}
.loginform .custome-input{ width: 200px;}
.loginform #other_state_text input{width: 175px;}
.includedFree li {padding:5px 0;}
.loginform #rememberme input {width:auto; outline:none; border:0;}
#forgotpassword {position:relative; top:-15px; left:210px;}
#ExistingUserLogin #forgotpassword{left:51px; top:0px;}
#resellerforgotpassword {
position: relative;
top: 0px;
left: 65px;
}
.loginform #rememberme label {
	font-weight:normal;
	font-size:12px;
	width:auto;
	text-align:left;
	display:inline;
	float:none;
	clear:none;
	margin:0;}
.formError {
	width:85%;	
	font:bold 13px Arial, Helvetica, sans-serif;
	background: #ffdbdb url(/getImage.php?src=ico-warning.gif) no-repeat 5px 10px;
	border: 2px solid #ffbaba;
	padding: 10px 0 8px 28px !important;
	margin: 10px 0;
	color: #6b6868;
	text-align:left;}
.formError p{padding:0 0 3px 0;}

/* Regular Continue Buttons */

.submitbtn {
	font-size:16px;
	font-weight:bold;
	line-height:40px;
	height:39px;
	text-shadow:0 1px 1px rgba(0, 0, 0, 0.25);
	background: 0px 0 url('/getImage.php?src=bg_green_btn_med.png') #91BD09;
	border:1px solid rgba(0, 0, 0, 0.25);
	color:#FFFFFF;
	cursor:pointer;
	display:inline-block;
	position:relative;
	text-decoration:none;
	-moz-border-radius:6px;
	width:auto;
	padding:0 18px;}
.submitbtn:hover {background-position: 0px -40px;}
.submitbtn:active {background-position: 0px -78px;}
.submitbtnDisabled {text-shadow:0 1px 1px rgba(0, 0, 0, 0.25); background: 0px -117px url('/getImage.php?src=bg_green_btn_med.png') #91BD09; border:1px solid rgba(0, 0, 0, 0.25); color:#FFFFFF;}
.submitbtnDisabled:hover {background-position: 0px -117px;}
.submitbtnDisabled:active {background-position: 0px -117px;}

/* Large Buy Buttons */

.submitbtn_lrg {
	font-size:12px;
	font-weight:bold;
	line-height:12px;
	height:55px;
	text-shadow:0 1px 1px rgba(0, 0, 0, 0.25);
	background: 0px 0 url('/getImage.php?src=bg_green_btn_lrg.png') #91BD09;
	border:1px solid rgba(0, 0, 0, 0.25);
	color:#FFFFFF;
	cursor:pointer;
	display:inline-block;
	position:relative;
	text-decoration:none;
	-moz-border-radius:6px;
	width:auto;
	padding:0 18px;}
.submitbtn_lrg strong {font-size:18px;}
/* Grey Secondary Buttons */

.submitBtn_grey {
	font-size:11px;
	font-weight:bold;
	background: #EEE;
	border:1px solid #BBB;
	color:#999;
	cursor:pointer;
	display:inline-block;
	position:relative;
	text-decoration:none;
	-moz-border-radius:6px;
	width:auto;
	height:23px;
	line-height:22px;
	padding:0 4px 0;}
.submitBtn_grey:hover {color:#666; border:1px solid #999;}
.submitBtn_grey:active {background-position: 0px -44px;}
 input::-moz-focus-inner {border: 0;}
button::-moz-focus-inner {border: 0;}
#alsoAvailable ul {margin:0; padding:0;}
#alsoAvailable ul li {margin:3px 0;}
#alsoAvailable p {padding:0;}
/* -- Payment Options -- */

#CartSection_paymentOptions {margin:0 0 10px 0; overflow:hidden; position:relative;}
#CartSection_paymentOptions .opt-1{ width:460px;}
#CartSection_paymentOptions .opt-2{ width:412px;}
.pay-sepr{background:url(/getImage.php?src=ver-dot-line.jpg) repeat-y 470px 0;}
.pay-sep-or span{ background:#fff; font:normal 15px Arial, Helvetica, sans-serif; color:#5b5b5b; position:absolute; top:42%; width:25px; padding:20px 0; display:inline-block; left:460px;}
div.paymentOption {margin:10px 0 0 0; position:relative;}
ul#payment_options_online {padding:0; margin:0 0 0 15px;}
ul#payment_options_online li.paymentOption {float:left; margin:0; border-bottom:2px dotted #d6d6d6; padding:7px 0;}
ul#payment_options_online li.last-paymentOption {border-bottom:0;}
ul#payment_options_online li.paymentOption input {display:block; float:left; margin:17px 8px 0;}
ul#payment_options_online li.paymentOption label {
	display:block;
	float:left;
	padding:11px 0 11px 100px;
	margin:0;
	width:380px;
	background: 0px 12px url('/getImage.php?src=payment_option_logos.gif') no-repeat;
	vertical-align:middle;
    cursor:pointer;
	font:normal 15px Arial, Helvetica, sans-serif;
	color:#666;}
ul#payment_options_online li.paymentOption label span.hint {padding-left:5px;}
ul#payment_options_online li#payment_options_custom span {display:block; font-style:italic; padding:0; font-weight:normal;}
ul#payment_options_online li#payment_options_custom input {margin-top:16px;}
ul#payment_options_online li#payment_options_custom a {color:#777; text-decoration:underline;}
ul#payment_options_online li#payment_options_custom a:hover {text-decoration:none;}
ul#payment_options_online li.active {background:#f7faeb;}
ul#payment_options_online li.selected {background:#e8f5cf;}
/* -- Payment Option Logos -- */

ul#payment_options_online li.paymentOption label#label_custom {background-position:0 -126px;/* Net Banking */}
ul#payment_options_online li.paymentOption label#label_paypalapi {background-position:0 -83px;/* AMEX */}
ul#payment_options_online li.paymentOption label#label_paypal {background-position:0 -79px;/* Paypal  */}
#payment_options_online li:last-child{ border:none !important;}
/* -- Net Banking Hover Over -- */

#paymentOption_online {z-index:999;}
#paymentOption_offline {z-index:1;}
ul#payment_options_online li.netbanking_helptext {float:none; clear:left;}
ul#payment_options_online li.netbanking_helptext a:link, ul#payment_options_online li.netbanking_helptext a:visited {
	font-size:11px;
	color:#666;
	display:block;
	text-decoration:underline;
	width:240px;}
#offline_pay_country {padding:2px;}
/* -- right Aligned Form Form -- */

.frmSignupSplit td {vertical-align: top;}
.rightAlignedForm label {
	color:#626262;
	font:normal 17px Arial, Helvetica, sans-serif;
	margin:0 4px 0 0;
	display:block;
	margin-bottom:4px;}
.rightAlignedForm td.frmLabel {width:130px; text-align:right;}
table.frmTable {width:90%;}
table.frmTable tr td.frmLabel, table.frmTable tr td.frmField {clear:both; padding:10px 0; vertical-align:top;}
table.frmTable tr td.frmSecondField {padding-left:10px;}
table.frmTable tr td.frmSubmit {padding:12px 0 12px 0px;}
table tr td.frmCancel {padding:10px 12px 0 0px; border-top: 1px solid #eee;}
table tr td.frmCancel a {margin-top: 15px; float:right;}
/* Styles for all right-aligned forms */
/* ---------------------------------- */
.rightAlignedForm .customerrightAlignedForm select {width: 200px;}
table.frmTable tr td.customerrightAlignedForm {padding-right: 10px;}
.rightAlignedForm #other_state_text input{ width: 175px;}
.rightAlignedForm input.textbox, .rightAlignedForm textarea, .rightAlignedForm select {width:296px;}
.rightAlignedForm #input_address2 {margin: 8px 0 0 0px}
.rightAlignedForm #input_phone {width: 215px}
.rightAlignedForm textarea {width: 298px;}
.rightAlignedForm select {width: 304px;}
.rightAlignedForm input.minitextbox {width: 136px;}
.rightAlignedForm .required {color:#696969;}
.rightAlignedForm input.text_input {height:20px; line-height:24px;}
.rightAlignedForm .frmHint {padding:2px 0 0 0; margin:1px 0 0; color:#999; display:inline-block; font:normal 12px Arial, Helvetica, sans-serif;}
.rightAlignedForm .aboveField {float:right; margin:-18px 27px 0 5px;}
.rightAlignedForm label.error, .loginform label.error {
	background:none;
	border:medium none;
	clear:both;
	color:#FF0000;
	display:block;
	float:none;
	font-weight:normal;
	line-height:13px;
	margin:4px 0 0 0;
	padding:0;
	font:normal 12px Arial, Helvetica, sans-serif;}
.rightAlignedForm input{ width:280px; }
.rightAlignedForm label.error {
	text-align:left;
	width:auto;
	background:url(/getImage.php?src=cross.png) no-repeat;
	padding-left:20px;
	margin-top:5px;	}
.chk-icons label.error {
	text-align:left;
	width:auto;
	background:url(/getImage.php?src=cross.png) no-repeat;
	padding:0 0 0 20px;
	margin:0 0 0 5px;
	border:0;
	color:#FF0000;
	font:normal 12px/5px Arial, Helvetica, sans-serif;
	}	
.chk-icons .frm-field, .chk-icons .frm-select{ margin:0;}	
.loginform label.error {
	text-align:left;
	width:auto;
	background:url(/getImage.php?src=cross.png) no-repeat;
	padding-left:20px;}
.rightAlignedForm label.checked {background:url(/getImage.php?src=tick.png) no-repeat; height:16px; display:inline-block; margin-left:5px;}
input[name=contact_email][aria-invalid=false] + label{background:url(/getImage.php?src=tick.png) no-repeat; height:16px; display:inline-block; margin-left:5px; border:0; padding:0 0 0 16px; }

.loginform label.checked {background:url(/getImage.php?src=tick.png) no-repeat;	 display:inline-block; height:16px; margin-left:5px;}
.rightAlignedForm input.error, .loginform input.error {
	border:1px solid #FF8080;
	background:#fff;
	margin:0;
	font-weight:normal;
	color:#696969;}
.loginform input.error {padding:4px; background:#FFF;}
.rightAlignedForm textarea.error, .loginform textarea.error {border:1px solid #FF8080; background:#FFF; margin:0; font-weight:normal;}
.rightAlignedForm input.required {color:#696969;}
/* New Customer Registration Form */

#input_phone_cc,#input_mobile_cc {width:30px; text-align:center;}
#input_phone, #input_mobile {width:203px;}
.optionalField {color:#ccc;}
#input_address2 {margin:9px 0 10px 0px;}
/* Domain Search Results */

.domainSearchResult {overflow:hidden;}
.pageHeaderWide {
	width:100%;
	background: left bottom no-repeat url('/getImage.php?src=bg_page_header.png') #FFF;
	height:60px;
	line-height:60px;
	clear:both;
	position:relative;}
.pageHeaderWide .help-link {
	float:right;
	padding:34px 20px 0 0;
	font-size:13px;
	font-weight:bold;
	line-height:normal;}
.testimonial {padding:0 0 0 15px;}
.testimonial .desc {color: #555555; font: italic 15px "Times New Roman", Times, serif; padding-bottom: 10px;}
.testimonial h2 {
	background:none;
	color:#656565;
	font-size:23px;
	padding-top:0;
	padding-left:0;}
.testimonial .author {color: #7F7F7F; font: italic 14px "Times New Roman", Times, serif; padding-bottom:35px;}
.green {color:#3B9D00;}
.domainStatusNotAvailable {color:#B80028;}
.domainSearchResult h3.searchAgain {
	font:bold 16px Arial, Helvetica, sans-serif;
	margin:12px 0 10px 25px;
	float:none;
	color:#5b5b5b;}
.selectFromAlternatives {
	padding:0px;
	font:normal 18px Arial, Helvetica, sans-serif;
	margin:10px 0 0 10px;
	color:#5b5b5b;}
#link_newSearch {position:absolute; top:6px; right:14px;}
#primaryDomain h2 {
	font:bold 30px Arial, Helvetica, sans-serif;
	color:#6fc61e;
	max-width:700px;
	word-wrap:break-word;
	padding:0;}
.domainSearchResult .duration {float:right; font-size:13px; padding:0;}
.domainSearchResult .duration label {font:bold 12px Arial, Helvetica, sans-serif; color:#5f5f5f; padding-right:5px;}
.domainSearchResult .duration select, .hosting-plans .plan-duration select {border:1px solid #c9c8c9; padding:1px; color:#444; width:185px;}
.domainSearchResult .duration .saving, .SavingsShow {
	color:#6FC61E;
	display:block;
	text-align:left;
	font-size:14px;
	padding-top:5px;
	padding-left:78px;}
.alternativeDomain {
	clear:both;
	margin:10px 0;
	margin:0 0 20px 10px;
	padding-top:20px;
	border-top:2px solid #EEEEEE;}
.freeHighlight {color:#E58942;}
a.remove_item_class:link, a.remove_item_class:visited {color:#93ACC0; text-decoration:none;}
a.remove_item_class:hover, a.remove_item_class:active {color:#005CBD;}
.CartItem:hover a.remove_item_class {color:#005CBD;}
.wrapper1 a:link {color:#FFFFFF; text-decoration:none;}
/* FREE STUFF */

.includedFree {clear:left; width:400px; color: #6d6d6d;}
.domainSearchResult .includedFree h3 {padding:0; color:#333;}
.includedFree h4 {padding:1px 0; margin:0 0 1px 0; color:#919191; font:normal 13px Arial, Helvetica, sans-serif;}
.includedFree p {padding:0; margin:0 0 5px 0;}
ul.includedFree {margin:0; padding:0;}
ul.includedFree li {font:normal 13px Arial, Helvetica, sans-serif; color:#727272; padding:3px 0 3px 18px; background:url('/getImage.php?src=id-tic-gray.png') no-repeat 0 6px;}
.hideIncludedFree ul li {display:none;}
ul.includedFree li.showmore, ul.includedFree li.showless {font-size:12px; padding:2px 0; background:none;}
p.quickSummary {width:350px;}
/* -- Hide Show Links when expanded -- */

.showIncludedFree p.quickSummary {display:none;}
#primaryDomain h4 {font:normal 14px Arial, Helvetica, sans-serif; margin-bottom:5px; color:#727272;}
/* -- Show the first few items for the main domain -- */

#primaryDomain p.quickSummary {display:none;}
#primaryDomain .hideIncludedFree ul li {display:block;}
#primaryDomain .hideIncludedFree ul li.hidden {display:none;}
#primaryDomain .hideIncludedFree li.showless {display:none;}
#primaryDomain .hideIncludedFree li.showmore {display:block;}
#primaryDomain .hideIncludedFree li.showmore a, .showIncludedFree li.showless a {color:#216ee1; font:normal 13px Arial, Helvetica, sans-serif; padding:10px 0; display:inline-block;}
/* ---- Also Available ----- */

#alsoAvailable {
	float:right;
	margin:0;
	overflow:hidden;
	width:215px;
	word-wrap:break-word;}
#alsoAvailable ul {margin:8px 0 0; padding:0;}
#alsoAvailable ul li {margin:6px 0; overflow:hidden;}
#alsoAvailable p {padding:0;}
#alsoAvailable input {float:left; clear:left;}
#alsoAvailable label {cursor:pointer; float:right; width:175px;}
.actionRow {	
	background:#f4f4f4;
	border-radius:5px;
	-moz-border-radius:5px;
	-webkit-border-radius:5px;
	-ms-border-radius:5px;	
	height:75px;
	clear:both;
	margin:15px 0;
	border:1px solid #CECECE;
	text-align:right;
	padding:15px;
	float:right;
	width:345px;}
.multiDomain .actionRow .uiButton-2 {margin:0;}
.multiDomain .actionRow {
	border:1px solid #bbbbbb;
	position:fixed;
	width:908px;
	margin:15px 0;
	bottom:-15px;
	-moz-border-radius: 6px;
	-webkit-border-radius:6px;
	-ms-border-radius: 6px;
	border-radius:6px;
	height:55px;
	padding:15px;}
.multiDomain .actionRow p.selectAllBox {margin-top: 4px; color:#6F6F6F; font:bold 16px Arial, Helvetica, sans-serif; width:550px;}
.multiDomain span#total_curr, .multiDomain .PageTotal span#page_total {color:#6FC61E; font:bold 34px Arial, Helvetica, sans-serif;}
.discounted_price {color:#6FC61E; font:bold 34px Arial, Helvetica, sans-serif;}
.undiscounted_price {color:#708090; font:bold 28px Arial, Helvetica, sans-serif;text-decoration: line-through;}
p.PageTotal {
	font:bold 16px Arial, Helvetica, sans-serif;
	color:#6f6f6f;
	margin:0px 0 0;
	padding:5px;
	float:left;
	width:190px;}
span#total_curr {color:#6fc61e; font:bold 34px Arial, Helvetica, sans-serif;}
.PageTotal span#page_total {color:#6fc61e; font:bold 34px Arial, Helvetica, sans-serif; padding-left:4px;}
.page_total {color:#6fc61e; padding-left:4px;}
#btnDomainBuy {float:right;}
/* ---- Secondary Domains ----- */

.domainSearchResult input.checkbox {float:left; margin:4px 8px 0 0;}
.domainSearchResult h3 {
	float:left;
	font:normal 17px Arial, Helvetica, sans-serif;
	color:#7a7a7a;
	background: none;
	float:left;
	padding: 0px;
	margin:0px;}
.domainSearchResult h3 label {cursor:pointer;}
/* Secondary/Alternate Domains */

#secondaryDomain div.available {
	margin:10px 0 0 15px;
	border-top: 1px solid #f0f0f0;
	border-bottom: 0px;
	padding: 10px 0;}
#secondaryDomain .includedFree {margin-top:5px; clear:left;}
.singleDomainAvailable #secondaryDomain .includedFree {padding-left:0;}
/* Multiple Domain Search Results */

.dmn-avail .available h3{ color:#549E0F;}
.multiDomain div.available {margin:0; border-bottom: 1px solid #f0f0f0; padding: 10px 0; overflow:hidden;}
#domains-not-available, #domains-status-unknown {margin:0 0 10px 0;}
#domains-not-available h2.selectFromAlternatives, #domains-status-unknown h2.selectFromAlternatives {margin-left:0;}
#domains-not-available .group-content p, #domains-status-unknown .group-content p, #domains-status-invalid .group-content p {
	color:#7a7a7a; margin:0; padding:5px 0 15px 0; font:normal 20px Arial, Helvetica, sans-serif;}
.group-content .available .includedFree {padding-left:21px;}
#primaryDomain .includedFree {padding-left:0px;}


/* Check Domain blurb */
.transfer-domain-blurb {background: url(/getImage.php?src=tranfar-doamin-bg.gif) #fff no-repeat 0px bottom; height:179px;}
.transfer-domain-blurb .transfer-content {padding: 12px 10px 0px 22px; margin:0px;}
.transfer-domain-blurb .transfer-content h4 {color:#515151; font-size:21px; padding:0px; margin:0px;}
.transfer-domain-blurb .transfer-content p {color:#3c3c3c; font-size:12px; padding:0px;}
.transfer-domain-blurb .transfer-search {padding: 5px 10px 0px 22px;}
.transfer-domain-blurb .transfer-search .www {
	color:#6d6e6c;
	font-size:12px;
	font-weight:bold;
	float:left;
	padding-top:16px;
	width:30px;}
.transfer-domain-blurb .transfer-search .transfer-search-box {width:201px; height:32px; float:left;}
.transfer-domain-blurb .transfer-search .transfer-input {
	color:#6a6d6f;
	font-size:14px;
	font-weight:bold;
	margin-top:8px;
	margin-left:0px;
	width:210px;
	float:left;
	padding:4px 2px;/* border: 1px solid #e5e5e5; */}
.transfer-domain-blurb .transfer-search .transfer-submit {
	margin-left:232px;
	background:url(/getImage.php?src=transfer-but.gif) no-repeat 0px 0px;
	width:95px;
	height:32px;
	border:none;
	margin-top:-31px;
	float:left;}
.transfer-domain-blurb .transfer-search p.transfer-note {
	font-size:12px; color:#3c3c3c; padding:0px;	padding-top: 2px;}
.transfer-domain-blurb .transfer-search p.transfer-note a {color:#005cbd; text-decoration:underline;}
.transfer-domain-blurb .transfer-search p.transfer-note a:hover {text-decoration:none;}
.sidebar-hosting-blurb {background: url(/getImage.php?src=get-hosting-blurb-bg.jpg) no-repeat 0px 0px; height:274px;}
.sidebar-hosting-blurb h2 {padding: 15px 0 0 25px; margin:0px; background:none;}
.sidebar-hosting-blurb ul {padding: 8px 0 5px 25px; margin:0px;}
.sidebar-hosting-blurb li {
	background: url(/getImage.php?src=white-check.gif) no-repeat 2px 8px;
	padding: 5px 0px 4px 25px;
	font-size:14px;
	color:#ffffff;
	font-weight:bold;}
.sidebar-hosting-blurb p {
	color:#ffffff;
	font-size:16px;
	font-weight:bold;
	margin:0px;
	padding: 10px 0 0 25px;
	line-height:15px;}
.sidebar-hosting-blurb .price-hilight {padding: 8px 10px 5px 0px;}
.sidebar-hosting-blurb .price-hilight p {color:#ffe800; font-size:18px; font-weight:bold;}
.sidebar-hosting-blurb .price-hilight p a {padding: 0 0px 0 8px;}
.sidebar-email-blurb {background: url(/getImage.php?src=get-email-blurb-bg.jpg) no-repeat 0px 0px; height:201px; margin-top:15px;}
.sidebar-email-blurb h2 {padding: 15px 0 0 25px; margin:0px; background:none;}
.sidebar-email-blurb ul {padding: 4px 0 5px 25px; margin:0px;}
.sidebar-email-blurb li {
	background: url(/getImage.php?src=gray-check.gif) no-repeat 2px 8px;
	padding: 4px 0px 3px 25px;
	font-size:14px;
	color:#7c7d7b;
	font-weight:bold;}
.sidebar-email-blurb p {
	color:#434343;
	font-size:16px;
	font-weight:bold;
	margin:0px;
	padding: 5px 0 0 25px;
	line-height:15px;}
.sidebar-email-blurb .price-hilight {padding: 3px 10px 5px 0px;}
.sidebar-email-blurb .price-hilight p {color:#fa6809; font-size:18px; font-weight:bold;}
.sidebar-email-blurb .price-hilight p a {padding: 0 0px 0 8px;}
.sidebar-email-blurb .email-blurb {background: url(/getImage.php?src=get-email-blurb-bg.jpg) no-repeat 0px 0px; height:201px; width:435px; margin-top:15px;}
/*_________ Call Us Sidebar Blurb _________*/

.sidebar-head {background:#D7D7D7 url("/getImage.php?src=bg-new-sb-header-2.gif") repeat-x left top; border-bottom:1px solid #aeaeae; margin-top:20px;}
.sidebar-child-blurb-content {margin-left:53px}
.sidebar-footer {
	background: url("/getImage.php?src=bg-sidebar-footer.gif") no-repeat left top;
	width:230px;
	height:10px;
	overflow:hidden;}
.sidebar-head h2 {
	font-size:15px;
	color:#333333;
	font-weight:bold;
	padding:8px 0 8px 11px;
	background:none;}
.new-sidebar-blurb {margin:0 0 20px 0;}
.sidebar-content h3 {font-size:16px; margin:0; padding:0 0 3px; color:#333333;}
.sidebar-content p {padding:0 0 8px; margin:0;}
.sidebar-content .input-text {
	width:110px;
	vertical-align:middle;
	border:2px solid #cecece;
	padding:5px 4px;
	font-size:13px;
	color:#555555;}
.sidebar-content input.error {
	width:110px;
	vertical-align:middle;
	border:2px solid #cecece;
	padding:5px 4px;
	font-size:13px;
	color:#555555;
	margin:0;
	font-weight:normal;
	background:#FFF;}
.sidebar-content .go-button {
	margin-left:-6px;
	vertical-align:middle;
	background:0 0 url("/getImage.php?src=button-go.gif");
	width:34px;
	height:30px;
	border:0;
	overflow:hidden;
	text-indent:-1000000px;
	cursor:pointer;}
.sidebar-content .ui-divider {
	clear:both;
	background:#D0D0D0;
	height:1px;
	overflow:hidden;
	margin:0 0 12px;}
.live-chat-floated, .contact-us-floated {
	display: inline;
	float: left;
	margin: 0 0 15px 15px;
	width: 138px;}
.live-chat-floated h3, .contact-us-floated h3 {background: url("/getImage.php?src=side-bar-support-icons-tiny.gif") no-repeat scroll 0 0 transparent; padding-left: 35px !important;}
.contact-us-floated h3 {background-position: 5px -21px; padding-left: 10px !important;}
.contact-us-floated small {display: block;}
.left-side {margin-left: 140px; text-align: left;}
.left-side h4 {font-weight: bold; margin: 0;}
.left-side ul, .list-dot {color: #646464; font-size: 14px; margin: 5px 0; padding-left: 15px;}
.left-side ul li, .list-dot li {list-style-type: disc; padding: 2px 0;}
.right-side {display: inline; float: left; margin-right: 14px;}
.right-side .image {margin-bottom: 6px;}
#sidebar-child-haveUsCallYou label.error {
	float:right;
	clear:both;
	margin:0;
	padding:5px 0;
	border:none;
	color:#FF0000;
	font-size:11px;
	font-weight:normal;
	width:100%;
	display:block;}
.rbtop div {background: url("/getImage.php?src=sidebar-top-left-curve.gif") no-repeat top left;}
.rbtop div span {border-top:1px solid #d2d2d2; display:block; margin:0 6px;}
.rbtop {background: url("/getImage.php?src=sidebar-top-right-curve.gif") no-repeat top right;}
.rbbot div {background: url("/getImage.php?src=sidebar-bottom-left-curve.gif") no-repeat bottom left;}
.rbbot div span {
	border-bottom: 1px solid #e9e9e9;
	display:block;
	margin:0 6px;
	height:6px;
	background:#F6F6F6;}
.rbbot {background: url("/getImage.php?src=sidebar-bottom-right-curve.gif") no-repeat bottom right;}
/* height and width stuff, width not really nessisary. */
.rbtop div, .rbtop, .rbbot div, .rbbot {width: 100%; height: 7px; font-size: 1px;}
.sidebar-head h2 {
	border-left:1px solid #C9C9C9;
	border-right:1px solid #C9C9C9;
	color:#333333;
	font-size:15px;
	font-weight:bold;
	padding:1px 0 8px;
	text-align:center;}
.sidebar-content {
	border-left:1px solid #c9c9c9;
	border-right:1px solid #c9c9c9;
	background:#f6f6f6;
	padding:12px 8px 0;
	overflow:hidden;
	_height:1%;}
.sidebar-content .last {padding:0; margin-bottom:0; border:0;}
.sidebar-child-blurb {border-bottom:1px solid #e4e4e4; margin-bottom:7px; padding:0 0 14px;}
.sidebar-child-blurb-content {margin-left:53px}
.sidebar-footer {background: url(images/bg-new-sb-footer.gif) no-repeat left top; width:230px; height:10px; overflow:hidden;}
.sidebar-head h2 {
	font-size:15px;
	color:#333333;
	font-weight:bold;
	padding:1px 0 8px 11px;
	border-left:1px solid #C9C9C9;
	border-right:1px solid #C9C9C9;}
.sidebar-content h3 {font-size:16px; margin:0; padding:0 0 3px;	color:#333333;}
.sidebar-content p {padding:0 0 8px;}
.sidebar-content .input-text {
	width:130px;
	vertical-align:middle;
	border:2px solid #cecece;
	padding:4px;
	font-size:13px}
.sidebar-content .go-button {
	margin-left:-30px;vertical-align:middle}
.icon-support-contact-us, .icon-support-call-you, .icon-support-call-you-chat, .icon-support-email-us {
	background: url(images/sider-bar-support-icon.gif) no-repeat left top;
	display:block; width:44px; float:left; margin:4px 0 0 2px;}
.icon-support-contact-us {height:34px; background-position:16px top;}
.icon-support-call-you {height:41px; background-position:left -102px}
.icon-support-call-you-chat {height:36px; background-position:left -216px}
.icon-support-email-us {height:29px; background-position:left -321px}
.support-contact-number {font-size:13px; font-weight:bold; color:#9b9b9b;}
.support-contact-number span {font-weight:normal; font-size:13px;}
.sidebar-content h2, .sidebar-content .which-plan-heading .ui-title {
	background: url("/getImage.php?src=side-bar-info-icons.gif") no-repeat scroll 0 -91px transparent;
	color: #656565; font-size: 23px; font-weight: bold; padding-left: 72px;}
.sidebar-content .which-plan-heading .ui-title {padding-top:7px;}
.sidebar-content h2 small, .sidebar-content .ui-title small {display: block; font-size: 19px; font-weight: normal;}
.rbtop div {background: url("/getImage.php?src=sidebar-top-left-curve.gif") no-repeat top left;}
.rbtop div span {
	border-top:1px solid #d2d2d2;
	display:block;
	margin:0 6px;
	background: #F6F6F6;
	height:6px;}
.rbtop {background: url("/getImage.php?src=sidebar-top-right-curve.gif") no-repeat top right;}
.rbbot div {background: url("/getImage.php?src=sidebar-bottom-left-curve.gif") no-repeat bottom left;}
.rbbot div span {
	border-bottom: 1px solid #D6D6D6;
	display:block;
	margin:0 6px;
	height:6px;
	background:#F6F6F6;}
.rbbot {background: url("/getImage.php?src=sidebar-bottom-right-curve.gif") no-repeat bottom right;}
.rbcontent {
	background:none repeat scroll 0 0 #F6F6F6;
	border-left:1px solid #C9C9C9;
	border-right:1px solid #C9C9C9;
	overflow:hidden;
	padding:5px 10px 10px;
	text-align:center;}
/* height and width stuff, width not really nessisary. */
.rbtop div, .rbtop, .rbbot div, .rbbot {width: 100%; height: 7px; font-size: 1px;}
.page-note {font-size:11px; color:#515151;}

/*_________ Ui Buttons  _________*/

.actionRow .uiButton-2 {font:bold 25px Arial, Helvetica, sans-serif; height:55px; margin-top:7px;}
.ui-button-2 {
	height:55px;
	line-height:12px;
	font-size:12px;
	width:160px;}
.ui-button-2 span {height:55px; background-position: left -272px; text-align:center;}
.ui-button-2 span span {
	height:45px;
	_width:120px;
	padding-top:10px;
	background-position: right -329px;
	line-height:15px}
.ui-button-2:hover span {background-position: left -272px; background-color:transparent;}
.ui-button-2:active span {background-position:left -272px; background-color:transparent;}
.ui-button-2:hover span span {height:55px; background-position: right -329px; background-color:transparent;}
.ui-button-2:active span span {height:55px; background-position: right -329px; background-color:transparent;}
.ui-button-2 span strong {font-size:18px}
.ui-button-s {font-size:12px; height:22px;}
.ui-button-s span {background:url("/getImage.php?src=sprite-input.gif") no-repeat left -416px; height:22px;}
.ui-button-s span span {background:url("/getImage.php?src=sprite-input.gif") no-repeat right -440px; height:22px; line-height:22px; padding:0 8px 0 4px;}
/* White Secondary Buttons */

.ui-button-3 {
	background:url(/getImage.php?src=bg-ui-button-3.gif) no-repeat left top;
	width:102px;
	height:25px;
	line-height:25px;
	text-align:center;
	display:block;
	font-size:12px;
	text-decoration:none;
	color:#666666;
	text-shadow:1px 1px 1px #fff;
	cursor:pointer;}
a.ui-button-3:link, a.ui-button-3:visited {color:#666666;}
a.ui-button-3:hover {color:#5489B3;}
a.ui-button-3:active {color:#333;}
.ul-sign-bullet {padding:0 0 10px;}
.ul-sign-bullet li {background: url("/getImage.php?src=bullet-sign.gif") no-repeat scroll left 5px transparent; color: #4A4A4A; font-size: 14px; padding: 5px 0 5px 25px;}
.lfloat {float:left;}
.rfloat {float:right;}
.acenter {text-align:center;}
.aright {text-align:right;}
.relative {position:relative;}
.clear {clear:both;}
.or {font-size:18px; color:#535353; padding:190px 0 0 10px; text-shadow:1px 1px 1px #fff}
.ui-space {height:20px; overflow:hidden;}
.ui-space-2 {height:15px; overflow:hidden;}
.highlihgt {color:#EB5E24;}
.underline {text-decoration:underline;}
.ui-message-block {text-align:center; padding:20px;}
.ui-message-block h1 {font-size:40px;}
.ui-message-block h3 {color:#5c5c5c; font-size:20px;}
.fb-link a {color:#183278; font-size:16px;}
.icon-facebook-2 {
	background:url("/getImage.php?src=icon-fb-twitter.gif") no-repeat left top;
	display:inline-block;
	vertical-align:middle;
	width:43px;
	height:43px;
	margin:0 50px 0 9px;}
.icon-twiiter-2 {
	background:url("/getImage.php?src=icon-fb-twitter.gif") no-repeat left -47px;
	display:inline-block;
	vertical-align:middle;
	width:43px;
	height:43px;
	margin:0 0 0 9px;}
.ui-title {
	color:#5b5b5b;
	font-size:16px;
	font-weight:bold;
	padding:0 0 5px;}
.ui-title-2 {color:#79973c; font-size:26px; font-weight:bold;}
.ui-title-3 {color:#5b5b5b; font-size:22px; font-weight:bold; padding:0 0 5px;}
.ui-title-4 {color:#5b5b5b; font-size:28px; font-weight:bold; padding:0 0 5px;}
.ui-title-5 {font-size:12px; font-weight:bold; padding:0 0 5px;}
.ui-number-bullet {
	border-right:3px solid #d0d0d0;
	font-size:36px;
	color:#5b5b5b;
	line-height:30px;
	padding:0 3px 0 0;
	margin:0 4px 0 0;}
.width50 {width:50%;}
.font-large {font-size:15px}
.font-xlarge {font-size:16px;}
.font-xxlarge {font-size:18px;}
.font-xxxlarge {font-size:24px;}
.font-normal {font-weight:normal;}
.adons .ui-heading {font:bold 28px 'Rokkitt', serif;}
.adons .fea-heading{font:bold 16px 'Rokkitt', serif;}
.ui-heading {
	font:bold 32px 'Rokkitt', serif;
	text-shadow: 1px 1px 1px #ececec;
	margin:3px 0;
	color:#5B5B5B;
	padding:0 0 15px 0;
	margin:0;}
.ui-heading-2 {
	font-size: 44px;
	font-weight: bold;
	color:#444444;
	margin:10px 0 20px;}
.ui-label {color:#EE7214;}
.ui-subtitle {font:normal 20px 'Rokkitt', serif; margin:0 0 13px 0; color:#727272;}
.ui-divider {
	height:1px;
	overflow:hidden;
	background:#d3d3d3;
	margin:10px 0 10px 0;}
.ui-box {background:#f9f9f9; padding:8px; margin:0 0 15px;}
.ui-blurb {border:1px solid #D7D7D7; background:#F7F7F7; padding:12px 20px;}
.inline-note {color:#a1a1a1}
.inline-hightlight {
	display:inline-block;
	background:#fcf3b5;
	border-bottom:1px solid #d7d7d7;
	border-right:1px solid #dadada;
	padding:0 3px;}
.form-list {margin:0 0 10px; overflow:hidden;}
.form-list li {padding-bottom:10px; overflow:hidden; clear:both;}
.form-list input, .form-list label {vertical-align:top; float:left;}
.form-list input {margin:0;}
.form-list label {width:95%; margin:0 0 0 5px; line-height: 16px;}
.p-block {margin:0; padding-bottom:12px; padding-top:6px;}
.ui-highlight {background:#F7862B; color: #FFFFFF; font-weight: bold; padding: 2px 4px;}
.ui-text-highlight {color:#B6391B; font-weight:bold; font-size:14px;}
.table-data a {color: #377CE4; text-decoration:none; font-weight:bold;}
.table-data thead .tld-col {color: #393939; font-size: 18px;}
.table-data td {text-align:center; color:#828282;}
.table-data thead th{
  background-color:#EDEDED;
  border-bottom-color:#FFFFFF;
  border-bottom-style:solid;
  border-bottom-width:2px;
  color:#0F0F0F;
  font:normal normal bold 17px/normal Arial, Helvetica, sans-serif;
  text-align:center;
  padding:9px;	}
.table-data thead td {
	background: #5e5e5e;
	border-color: #FFFFFF #FFFFFF #DDDDDD;
	border-style: solid;
	border-width: 1px;
	color: #fff;
	padding: 8px 0;
	font-size: 15px;
	font-weight: bold;}
.table-data tbody td {
	color: #4C4C4C;
	font:normal 15px Arial, Helvetica, sans-serif;
	padding:9px;
	border-bottom:solid 1px #ddd;}
.table-data tbody tr.alternate {background: none repeat scroll 0 0 #E6E6E6;}
.table-data .odd td { background-color:#F6F6F6;}
 .table-data .even td { background-color:#fff;}
.price {font-size: 22px; font-weight:bold;}
.per-month {font-size: 20px; font-weight:normal;}
.ul-li-bdr li {
	border-bottom: 1px solid #DADDD6;
	font-size: 14px;
	padding: 8px;}
.ul-li-bdr {margin: 0 20px; padding: 10px 0;}
.free-features-title {
	background: url("/getImage.php?src=orange-round-corners.gif") no-repeat scroll left top #FD8907;
	color: #FFFFFF;
	float: left;
	font-size: 14px;
	height: 27px;
	margin: 0 0 20px 12px;
	overflow: visible;
	padding: 0;
	position: relative;
	text-align: center;}
.free-features-title .right-round {
	background: url("/getImage.php?src=orange-round-corners.gif") no-repeat scroll right -27px #FD8907;
	float: left;
	margin-left: 6px;
	padding: 5px 7px 5px 0;}
.free-features-title .arrow {
	background: url("/getImage.php?src=feature-title-arrow.gif") no-repeat scroll 0 0 transparent;
	height: 9px;
	left: 11px;
	position: absolute;
	top: 27px;
	width: 11px;}
p.successMessage {
	background:#FEFF9F;
	padding:5px;
	margin:5px 0;
	display:none;
	clear:both;}
.icon-support-contact-us, .icon-support-call-you, .icon-support-call-you-chat, .icon-support-email-us {
	background: url("/getImage.php?src=sider-bar-support-icon.gif") no-repeat left top;
	display:block;
	width:44px;
	float:left;
	margin:4px 0 0 2px;}
.icon-support-contact-us {height:34px; background-position:16px top;}
.icon-support-call-you {height:41px; background-position:left -102px}
.icon-support-call-you-chat { height:36px; background-position:left -216px}
.icon-support-email-us {height:29px; background-position:left -321px}
.support-contact-number {font-size:13px; font-weight:bold; color:#666;}
.support-contact-number small {font-weight:normal; font-size:11px;}
.support-contact-tollfree {color:#FA7109; font-size:14px;}
#resultSidebar {float:right; width:215px; overflow:hidden;}

/*_________ The Most Excellent Testimonials _________*/

.testimonials {margin:0 auto; padding:8px 0px 50px 0px; clear:both;}
.testimonials blockquote {padding:1px 0 0 24px; background:url(/getImage.php?src=quotes-start.gif) no-repeat 0px 0;}
.testimonials blockquote p {
	font-size:16px;
	font-style:italic;
	line-height:25px;
	color:#616161;
	padding:0px;
	padding-bottom:5px;}
.testimonials blockquote p.cite {
	text-align:right;
	font-size:12px;
	color:#616161;
	font-style: italic;
	line-height:16px;}
.testimonials blockquote p.cite strong {font-size:16px;}
/* Main Content Ends */

/* -- Post Order Page -- */

.postOrder_Success {text-align:center;}
.postOrder_Success h1 {text-align:center; font-size:28px; margin-bottom:15px;}
.postOrder_Success h2 {
	background:none;
	color:#434343;
	font-weight:normal;
	font-size:24px;
	padding:0;}
.postOrder_Success p {font-size:13px; margin:0; text-align:center;}
.postOrder_Success h3 {margin-top:20px; border-bottom: none;}
p.ItemDiscTotal {color: #70A750;}
p.ItemDiscTotal_upgrade {color: #70A750;}
.ItemConvertedSubtotal {font-size:12px;}
.ItemConvertedSubtotal span#CartTotal, .ItemConvertedSubtotal span#CartTotal #total {color:#333; font-size: 12px;}
.ItemDiscountTotal {font-size:12px; color: #70A750;}
.ItemTotalAfterDiscount {margin-top:5px; padding:5px 0; width: 240px;}
.ItemTotalAfterDiscount #TotalAmount {color: #6fc61e; padding-left:5px;}
.couponLoading {
	background: url("/getImage.php?src=load.gif") no-repeat left top;
	width:16px;
	height:16px;
	display:inline-block;
	vertical-align:hidden;
	visibility:hidden;}
/* homepage show loading */
.modal_overlay {
	background:#000;
	top:0;
	left:0;
	right:0;
	bottom:0;
	z-index:1000 !important;
	opacity:0.7;	
	filter: alpha(opacity=70);

	/*-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=70)";
	filter:alpha(opacity=70);	*/
	position:absolute ;
	height:100%;
	width:100%;}
.modal_content {
	width:400px;
	background:#fff;
	text-align:center;	
	position:absolute;	
	left:50%;
	top:60px;
	z-index:1001 !important;
	margin-left:-200px;
	border-radius:5px;
	-moz-border-radius:5px;
	-webkit-border-radius:5px;	
	-ms-border-radius:5px;		
	border:solid 3px #86aac8;	}
.wide_modal {width:790px; margin-left:-400px;}
.extra_wide_modal {width:670px; margin-left:-345px;}
.modal_content .pre {background: url("/getImage.php?src=30preloader.gif") #ffffff no-repeat left top; width:160px; height:20px;	margin:0 auto;}
.modal-box-header {position:relative;}
.modal-label {font-size: 12px; color:#444444; font-style:italic; margin:0 0 20px;}
.modal-box-header h1, .modal-box-header .modal-box-h1 {
	font-size:15px;
	line-height: 25px;
	font-weight:normal;
	padding:10px;
	background:transparent url("/getImage.php?src=modal-title-bg.gif") repeat-x 0 100%;
	text-shadow:0 2px 0 #FFFFFF;
	color:#5B5B5B;}
.help-note {
	position: absolute;
	right:10px;
	top:15px;
	font-size:11px;
	color:#444444;}
.modal-body {padding:24px 16px; text-align:left;}
.modal-body p {padding-left:0; margin-top:0;}
.form-field {clear:both; margin:0 0 12px;}
.form-field .input {
	border-left:1px solid #cccccc;
	border-right:1px solid #cccccc;
	border-top:1px solid #969696;
	border-bottom:1px solid #e7e7e7;
	width:300px;
	padding:4px}
.form-field .input-2 {
	border:1px solid #D5D5D5;
	padding:4px;
	font-size:16px;
	width:330px}
.form-label {
	float:left;
	width:120px;
	text-align:right;
	font-size:13px;
	font-weight:bold;
	vertical-align:middle;}
.form-label-2 {
	display:block;
	font-size:16px;
	margin-bottom:10px;
	float:left;
	width:140px;
	text-align:right;
	padding:5px 0 0;}
.form-input {margin:0 0 0 135px; vertical-align:middle;}
.mandatory {color:#fd5444;}
.form-field .error {background:none; border:1px solid #FF8080; margin:0;}
.form-field label.error {
	border:none;
	display:block;
	padding:0;
	margin:0;
	font-weight:normal;
	color:#FF0000;}
.form-field .required {color: #434343;}
.input-wrp {margin-left:170px}
.logo-img img, .logo-img a {display:inline-block; outline: medium none; vertical-align:middle;}
.icon-facebook, .icon-twitter, .icon-blog {
	background:  url("/getImage.php?src=twitter-facebook-blog.jpg") no-repeat left top;
	display:inline-block;
	text-indent:-9999em;
	margin-right:5px;}
.icon-facebook {background-position: top left; width:25px; height:25px;	margin-left:10px;}
.icon-twitter {background-position: -30px top; width:25px; height:25px;}
.icon-blog {background-position: -63px top; width:75px; height:25px;}
.orange-text {color:#fd8602;}
/* Blurb body */
.offerBlurb {
	color:#666666;
	font-size:11px;
	font-weight:bold;
	width:261px;
	margin-top:2px;}
.offerBlurb .blurbTop, .offerBlurb .blurbBottom {height:17px; background:url("/getImage.php?src=blurb-images.gif") no-repeat left top;}
.offerBlurb .blurbBody {border:1px solid #d3d3d2; border-width: 0 1px; padding: 0 10px;}
.offerBlurb .blurbBottom {background-position:left -17px;}
/*****************/
/*Hosting Modals*/
/***************/
.textbox-error {border:1px solid #db0606 !important; color: #db0606 !important;}
.hosting-modal {position:relative;}
.hosting-modal-title {padding: 15px 10px 15px 10px; background:#c6d2dc;
    border-bottom:1px solid #f1f1f1;
	color:#424242;
	font:bold 25px Arial, Helvetica, sans-serif;
	text-shadow:1px 1px 1px #fff;
	-moz-border-radius:5px 5px 0 0;
	-webkit-border-radius:5px 5px 0 0;
	text-align:center;}
a.modal_close {
	position: absolute;
	top: 5px;
	right: 5px;
	text-decoration: underline;
	text-decoration:none;
	font:bold 12px Arial, Helvetica, sans-serif;}
.hosting-modal-body {text-align:left;padding:24px 16px;}
	
/* Supported banks modal */
#netbanking_helptext_overlay {color:#5b5b5b; padding:0 10px 10px 10px;}
#netbanking_helptext_overlay .modal_content {text-align:left;}
#netbanking_helptext_overlay h3 {border-bottom:0; font-weight:bold; margin:0 0 10px 0;}
ul.vertical-list {
	float:left;
	margin:0 0 25px 0;
	padding:0 0 0 17px;
	width:195px;}
ul.vertical-list li {
	list-style-type:disc;
	font-weight:bold;
	font-size:12px;
	line-height:18px;}
	
/* Live Chat Pop Up */

#popup_live_chat {
	background: 0 0 url('/getImage.php?src=livechat.png') no-repeat;
	/* for ie6 show gif image */
    _background: 0 0 url('/getImage.php?src=livechat.gif') no-repeat;
	position:fixed;
	left:10px;
	bottom:10px;
	top: auto;
	z-index:1000;
	width:306px;
	height:153px;
	overflow:hidden;
	display:none;}
#popup_live_chat_topCloseLink {
	color: #692F9D;
	display: block;
	float: right;
	font-size: 12px;
	height: 32px;
	margin: 0;
	overflow: hidden;
	text-align: right;
	text-decoration: underline;
	text-indent: 999px;
	width: 38px;}
#popup_live_chat_bottomCloseLink {
	bottom: 21px;
	display: block;
	height: 20px;
	left: 119px;
	line-height: 999px;
	overflow: hidden;
	position: absolute;
	text-indent: 9999px;
	width: 100px;}
#popup_live_chat_openLink {
	top:37px;
	display:block;
	height:149px;
	left:3px;
	position:absolute;
	width:270px;
	overflow:hidden;}
#upsell_modal {text-align:left; position: absolute;}

#upsell_modal h1, .modal_content h1{
	padding: 15px 0;
	background:#c6d2dc;
    border-bottom:1px solid #f1f1f1;
	color:#424242;
	font:bold 26px Arial, Helvetica, sans-serif;
	text-shadow:1px 1px 1px #fff;
	-moz-border-radius:5px 5px 0 0;
	-webkit-border-radius:5px 5px 0 0;
	-ms-border-radius:5px 5px 0 0;
	border-radius:5px 5px 0 0;		
	text-align:center;}
#upsell_modal h2 {
	font:bold 16px Arial, Helvetica, sans-serif;
	text-shadow:1px 1px 1px #fff;
	text-align:center;
	color:#404040;
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #faf9fa), color-stop(1, #e3e2e3) );
	background:-moz-linear-gradient( center top, #faf9fa 5%, #e3e2e3 100% );
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#faf9fa', endColorstr='#e3e2e3');
	background-color:#faf9fa;
	padding:8px 0 7px 30px;}
.upsell-modal-body {padding:30px 20px;}
#upsell_modal .upsell-option {padding: 0 10px;}
#upsell_modal .option-icon {background:transparent url("/getImage.php?src=upsell-modal-icons.gif") repeat-x 0 -62px; float:left; width:69px; height:69px;}
#upsell_modal .web-designer-option .option-icon {background-position: 0 -62px;}
#upsell_modal .web-hosting-option .option-icon {background-position: -69px -62px;}
#upsell_modal .email-hosting-option .option-icon {background-position: -138px -54px;}
#upsell_modal .option-details {float:left; margin-left:18px;}
#upsell_modal .option-details h3 {color: #fa7109; padding:0; font-size: 22px; float:none;}
#upsell_modal .option-details .byline-1, #upsell_modal .option-details .byline-2 {
	font-weight:bold;
	color:#646464;
	font-size:14px;
	line-height:18px;}
#upsell_modal .option-details .byline-2 {font-weight:normal; font-style: italic;}
#upsell_modal .option-pricing-details {float:right; color:#404040; width:200px;}
#upsell_modal .option-pricing-details h3 {float:none; padding:0; color:#404040; font-size:16px;}
#upsell_modal .option-amount {font-size:22px; font-weight:bold;}
#upsell_modal .option-link {
	background:url("/getImage.php?src=live-but-bg-live-help.gif") no-repeat scroll 0 0 transparent;
	text-align:center;
	color:#fff;
	width:71px;
	padding:4px 0;
	display:block;
	margin-top:3px;}
.or-seperator {
	height:40px;
	background:url("/getImage.php?src=bg_or_seperator.gif") repeat-x scroll 0 20px transparent;
	margin:0 0 0 14px;
	overflow:hidden;
	position:relative;}
.or-seperator p {
	position:absolute;
	padding:0 5px;
	margin:0;
	background:#fff;
	overflow:hidden;
	top:13px;
	left:5px;}
.no-thanks-link {
	color:#2265ac;
	font:bold 22px Arial, Helvetica, sans-serif;
	display: block;
	text-align:center;
	padding:0 0 20px 0;
	text-align: center;
	text-decoration: underline;
	text-shadow:1px 1px 1px #fff;}
.no-thanks-link:hover {text-decoration:none;}
.no-thanks-link span {font-size:18px; font-weight:bold;}
#upsell_modal .nothankslink {
	position: absolute;
	right: 10px;
	top: 19px;
	margin-right: 12px;}
.bold {font-weight:bold;}
.green {color: #6fc61e;}
.greytext {color: #da9259;}

/* connect with facebook popup related css */
#connect_facebook .hosting-modal-title {
	background: url("/getImage.php?src=modal-title-bg.gif") repeat-x scroll 0 100% transparent;
	color: #5B5B5B;
	font-size: 18px;
	font-weight: normal;
	line-height: 25px;
	padding: 12px 15px;
	text-shadow: 0 2px 0 #FFFFFF;}
#connect_facebook .hosting-modal-body {text-align:left; padding: 5px 15px 20px;}
.facebook-footer {
	margin-top:10px;
	border-top:2px solid #ebebeb;
	padding:10px 0 0;
	line-height:33px;}
.facebook-connect-cancel {float:left;}
.connect-btn-wrapper {float:right; color:#858585;}
.connect-btn-wrapper a {
	text-indent:-9999px;
	background:url("/getImage.php?src=connect-facebbok-btn.gif") no-repeat scroll 0 0 transparent;
	width:255px;
	height:38px;
	display:inline-block;
	margin-left:10px;}
/* Reseller dashboard*/

.dashbord-reseller {
    margin: 0px;
    padding: 0px;
    border:1px solid #9DAFC2;
}
.dashbord-reseller .heading {
	margin: 0px;
	padding: 2px 3px 3px 4px;
	color:#024381;
	text-align:left;
	font-weight:bold;
	font-size: 11px;
	}
.dashbord-reseller .heading a {
	color:#cc0000;
	text-align:left;
	font-weight:bold;
	font-size: 10px;
    }
.dashbord-reseller .heading a:hover {text-decoration:none;}
.dashbord-reseller .detail {margin-top: 0px; padding: 2px 1px 0px 3px; color: #2F2F2F; font-size: 11px;}
.dashbord-reseller .detail a {font-size: 9px; color: #2F2F2F; font-size: 11px; text-decoration: underline;}
.dashbord-reseller .detail a:hover {color: #2F2F2F; text-decoration: none;}
/* STYLES FOR PROMOTION ENGINE PAGES & ELEMENTS */

.new-tlds {background: #e7f2f9 !important;}
.new-tlds thead td {background: url(/getImage.php?src=new-tlds-background.gif) no-repeat left top !important;}
.promo-sidebar-blurb {background: #f8f8f8;}
.promo-sidebar-blurb thead td {
	margin:0px;
	padding:11px 0 11px 0px;
	background: url(/getImage.php?src=sidebar-header-bg.gif) #dfdfdf repeat-x;
	font-family: arial, sans-serif;
	font-size:14px;
	font-weight:bold;
	color:#434343;
	display:block;
	margin-top: 0 !important;
	margin-top: -5px;
	border-right:1px solid #d3d3d3;
	border-left:1px solid #d3d3d3;}
.promo-sidebar-blurb thead td a {color: #000; text-decoration: none;}
.promo-sidebar-blurb td {
	font-size: 11px;
	font-family: verdana;
	color: #4f4f4f;
	padding-left: 12px;
	background: url(/getImage.php?src=arrow-sidebar-list.gif) no-repeat 0 17px;}
.promo-sidebar-blurb td a {	color: #4f4f4f;}
.promo-sidebar-blurb td a:hover {color: #4f4f4f; text-decoration: underline;}
#my-nav .promo-my-nav {
	display: inline;
	padding-left: 13px;
	background: url(/getImage.php?src=sitebuilder-li.gif) no-repeat left 3px !important;
	background: url(/getImage.php?src=sitebuilder-li.gif) no-repeat left 2px;}
#my-nav .promo-my-nav a {
	background: none;
	color: #EB5300;
	padding: 0;
	font-weight: bold;}
#my-nav .promo-my-nav a:hover {color: #EB5300;}
.promo-tr {background: #F3FBF2;}
.strikeout {text-decoration: line-through;}
.promo-chck-avail {
	background: url(/getImage.php?src=tag-bg.png) no-repeat left 2px;
    color: #777;
    font: 12px Arial,Helvetica,sans-serif;
    margin-left: 10px;
    padding: 4px 12px;
    vertical-align: middle;}
.promo-chck-avail small a {color: #ff5400; font-size: 10px;}
.promo-chck-avail strong {font:normal 9px Arial, Helvetica, sans-serif; color:#555;}
.promo-chck-avail a {
	color: #cc0000;
	font-family: verdana;
	font-size: 10px;
	font-weight: bold;}
.promo-sidebar-header {
	background: #FBBE05;
	margin:0px;
	padding:4px 0 4px 12px;
	font-family:verdana, arial, sans-serif;
	font-size:11px;
	font-weight:bold;
	color:#fff;
	display:block;
	margin-top: 0 !important;
	margin-top: -5px;}
.promo-register {
	background: #FF9320;
	font-size: 10px;
	font-family: verdana;
	font-weight: bold;
	text-align: center;
	padding: 3px 5px;}
.promo-register a {color: #fff; text-decoration: none;}
.promo-register a:hover {color: #fff; text-decoration: underline;}
.promo-heading {font-family: arial; font-size: 15px;}
/*
SUPERSITE PROMO PAGE
*/

.promo-country-list {
	height:70px;
	overflow:auto;
	width:420px;
	border:1px solid #ccc;
	margin-left: 10px;}
.promo-country-list td {text-align: left;}
.promo-round-box-container {margin:0px; padding:0px;}
.promo-rounded-box-topcap {margin:0px; padding:0px; background: url(/getImage.php?src=rounded-box-topcap.gif) no-repeat center bottom; height:13px;}
.promo-rounded-box-content {
	margin:0 auto;
	padding:10px;
	border-left: 1px solid #565656;
	border-right: 1px solid #565656;
	width:738px;
	text-align:left;}
.promo-rounded-box-botcap {margin:0; padding:0; background: url(/getImage.php?src=rounded-box-bottomcap.gif) no-repeat center top; height:16px;}
/*html elements*/

label {padding: 0;}
input.textfield {padding: 2px 0 2px 0.25em; border: 1px #c8c7be solid;}
/*My elements*/
.myForm {font-size: 8px; height: 8px;}
.myFormEnds {font-size: 8px; height: 6px;}
.frmSection {
	height: 26px;
	margin: 0;
	padding: 0;
	font-family:  arial, verdana, sans-serif;
	font-size:12px;
	line-height:26px;
	color:#252525;
	font-weight:bold;
	display:block;
    background: #e9e9e9;
	padding: 5px 0;}
.frmSection h3 {
	margin: 0;
	padding: 0;
	padding-left: 15px;
	font-family:  arial, verdana, sans-serif;
	font-size: 16px;
	line-height: 26px;
	color: #434343;
	background: none;}
.frmSectionContent {
	font-family: arial, verdana, sans-serif;
	font-size:12px;
	line-height:15px;
	border:0px #cddae8 solid;
	text-align:left;}
.frmSectionContent li {
	list-style: none;
	font-family:  arial, verdana, sans-serif;
	font-size: 10px;
	line-height: 14px;
	margin: 0;
	padding: 0;
	background:  url(/getImage.php?src=pointer.gif) no-repeat left center;
	padding-left: 12px;
	margin-left: 4px;}
.frmLabel {text-align:left; font-size: 12px;}
.frmData {
	font-family:  arial, verdana, sans-serif;
	font-size: 11px;
	color: #C70101;
	line-height: 18px;
	border-bottom: 1px #e1e1e1 solid;
	font-weight: bold;}
/*
Buttons
---------
*/

.ui-button-grey, .ui-button-grey-gradient, .ui-button-white {
	background:none repeat scroll 0 0 transparent;
	border:medium none;
	color:#666666;
	cursor:pointer;
	display:inline-block;
	font-size:11px;
	font-weight:normal;
	height:26px;
	line-height:26px;
	margin:0;
	outline:medium none;
	padding:0;
	text-align:left;
	text-shadow:0 1px 1px rgba(0, 0, 0, 0.25);
	white-space:nowrap;
	width:auto;}
.ui-button-grey span, .ui-button-grey-gradient span, .ui-button-white span {
	background:url("/getImage.php?src=ui-button-grey.gif") no-repeat scroll left 0 transparent;
	display:block;
	_display:inline-block;
	height:26px;
	padding:0 0 0 10px;}
.ui-button-grey span span, .ui-button-grey-gradient span span, .ui-button-white span span {
	background:url("/getImage.php?src=ui-button-grey.gif") no-repeat scroll right -26px #F2F2F2;
	border:medium none;
	cursor:pointer;
	display:block;
	_display:inline-block;
	height:26px;
	line-height:26px;
	padding:0 20px 0 10px;}
.ui-button-grey-gradient span {background:url("/getImage.php?src=ui-button-grey-gradient.gif") no-repeat scroll left 0 transparent;}
.ui-button-grey-gradient span span {background:url("/getImage.php?src=ui-button-grey-gradient.gif") no-repeat scroll right -26px transparent;}
.gray-button {
	background:#eee;
	font:normal 15px Arial, Helvetica, sans-serif;
	padding:6px 20px;
	margin-left:10px;
	margin-top:-3px;
	color:#7a7a7a;
	border:solid 1px #c6c6c6;
	outline:none;
	border-radius:15px;
	-moz-border-radius:15px;
	-webkit-border-radius:15px;}
.ui-button-white span span {background:url("/getImage.php?src=ui-button-white.gif") no-repeat scroll right -26px #F2F2F2;}
.ui-button-grey span span a, .ui-button-grey-gradient span span {color:#666666;}
/* form with tabbed header elements inside the section content area */

/*
Inner plan style
---------------
*/
.inner-plan {
	margin: 0px;
	padding: 0px 0px 0px 12px;
	text-align: left;
	display: block;
	color:#5b5b5b;
	font:normal 14px Arial, Helvetica, sans-serif;}
.inner-plan-li {
	border: 0 none;
	margin: 0;
	padding: 0;
	color: #666;
	background: url(/getImage.php?src=inner-plan-li.gif) no-repeat left 5px;
	padding-left: 7px;}
	
/* VERSION 2: CSS STYLES */

.selected-products {
	background:#f5f5f5;
	padding:8px 0px 5px 20px;
	border:0px solid #cddae8;
	border-bottom: 0 none;}
.selected-products img {margin-right: 5px;}
.totalcost-wrapper {
	background: #FFFFD3;
	font-weight: bold;
	padding:10px 0;
	color: #FA6809;
	border-bottom: 1px #C8C86B solid;
	margin-top: 10px;
	font-size: 14px;}
.totalcost-wrapper .totalcost-icon {background: url(/getImage.php?src=ico-casbox.gif) #FFFFD3 no-repeat 5px 2px; padding-left: 19px;}
/* SUPERSITE COMMON CSS */

/*
HTML Elements
--------------------------
*/
.admin {background: url(/getImage.php?src=edit.gif) #FFFDF4 no-repeat 10px 5px;}
.admin-edit {border: 1px solid #ccc;}
img {border:0 none; padding:0; margin:0;}
h3 {
	color:#434343;
	font-size: 18px;
	font-weight:bold;
	margin: 0;
	padding:5px 0 0 15px;}
form {margin: 0px; padding: 0px;}
.indent {padding-left: 18px;}
a {text-decoration: none; color: #377ce4;}
a:hover {text-decoration: none; color: #FA7109; cursor:pointer;}
sup a {text-decoration: underline;}
sup a:hover {text-decoration: none;}
p {font:normal 12px/18px Arial, Helvetica, sans-serif; text-align: left; margin:0; padding:10px 0;}
.red-text {color:#CC0000;}
.green-text {color:#3B9D00;}
.highlight {background-color: #ffffcc;}
/*
My Navigation
----------------------------------------------------------------------------------------------
*/

#my-nav {background: url(/getImage.php?src=mynav-bg.gif) repeat-x left center;/*padding-top: 4px;*/}
#my-nav a {
	color: #476708;
	font: 10px verdana, arial, sans-serif;
	padding: 0 6px;
	text-decoration: underline;
	background: url(/getImage.php?src=ico-my-nav.gif) no-repeat left center;}
#my-nav a:hover {color: #4A413A; text-decoration: none;}
/*
Site Main Frame
-----------------------------------------------------------------------------------------------
*/

/*
Main Content Area {wrapper for "Content Area + Side Bar"}
*/
#maincontent {text-align: center; padding:0px; margin:0 auto;}
/*
Content Area
*/
#content {padding:0; background:#fff; text-align: left; position:relative;}
/*
SideBar
*/

#sidebar {width:230px; margin:0px auto; text-align: left; padding-left:20px;}
#sidebar .sidebar-bg {border-right:1px solid #d3d3d3; border-left:1px solid #d3d3d3; background:#f8f8f8;}
#sidebar .sidebar-header {
	margin:0px;
	padding:11px 0 11px 12px;
	background: url(/getImage.php?src=sidebar-header-bg.gif) #dfdfdf repeat-x;
	font-family: arial, sans-serif;
	font-size:14px;
	font-weight:bold;
	color:#434343;
	display:block;
	margin: 0px;
	border-right:1px solid #d3d3d3;
	border-left:1px solid #d3d3d3;}
#sidebar .list {
	margin:0px;
	padding:0px;
	border-right:1px solid #d3d3d3;
	border-left:1px solid #d3d3d3;}
#sidebar .list ul {
	margin:0;
	padding:10px 0;
	background:#f8f8f8;
	border-bottom:2px solid #ffffff;}
#sidebar .list li {
	margin:0;
	padding:0;
	list-style:none;
	font-family:verdana, arial, sans-serif;
	font-size:11px;
	color:#434343;
	line-height: 11px;}
#sidebar .list li a {
	text-decoration:none;
	padding: 5px 0px 5px 24px;
	display:block;
	color:#3085c8;
	background: url(/getImage.php?src=box-sidebar-list.gif) #f8f8f8 no-repeat 12px 8px;}
#sidebar .list li a:hover {
	color:#1e6297;
	text-decoration: underline;}
#sidebar .footer-border {
	padding-top:10px;
	background:#f8f8f8;
	border:1px solid #d3d3d3;
	border-top:0px;}
	
/* SIDEBAR BLURB fdns-styles.css */
#fdns-sidebar-blurb {
	width: 230px;
	overflow: hidden;
	font-family: arial;
	font-size: 13px;
	padding-top:15px;}
#fdns-sidebar-blurb .fdns-sb-header {background: url(/getImage.php?src=fdns-sb-header-bg.jpg) #aa340c no-repeat center top;}
#fdns-sidebar-blurb .fdns-sb-header h2 {
	margin: 0;
	padding: 12px 0 12px 14px;
	color: #ffffff;
	font-family: arial;
	font-size: 16px;
	font-weight: bold;
	line-height: 1.2;
	text-align:left;
	background:none;}
#fdns-sidebar-blurb .fdns-sb-header h2 em {color: #fed000; font-style: normal;}
#fdns-sidebar-blurb .fdns-sb-content {background:#f8f8f8; border-right:1px solid #d3d3d3; border-left:1px solid #d3d3d3;}
#fdns-sidebar-blurb .fdns-sb-content ul, #fdns-sidebar-blurb .fdns-sb-content li {margin: 0; padding: 0; list-style: none;}
#fdns-sidebar-blurb .fdns-sb-content ul {padding: 2px 12px 5px;}
#fdns-sidebar-blurb .fdns-sb-content li {
	font-weight: bold;
	background: url(/getImage.php?src=fdns-freeitems-icon.gif) no-repeat scroll left center;
	padding: 4px 0 4px 19px;
	font-size: 13px;
	color: #434343;}
.fdns-sb-footer {
	clear: both;
	background:#f8f8f8;
	border-right:1px solid #d3d3d3;
	border-left:1px solid #d3d3d3;
	text-align: right;}
.fdns-sb-footer a {
	text-decoration: none;
	color: #1c4c72;
	text-align: right;
	font-size:11px;
	background: url(/getImage.php?src=fdns-viewall-icon.gif) transparent no-repeat 93% 7px;
	display: block;
	padding: 4px 28px 10px 0;
	font-weight: bold;}
/*
Common Type Box Container
-----------------------------------------
*/
.box-container {
	height: 26px;
	margin: 0;
	padding: 0;
	font-weight: bold;
	display: block;
	background:  url(/getImage.php?src=iw-frmSection-bgblue.gif) #f9f9f9 repeat-x left top;
	text-align:left;
	cursor: pointer;}
.box-content {
	padding: 15px 15px 8px 15px;
	font-family: verdana, arial, sans-serif;
	font-size: 11px;
	line-height: 15px;
	border: 1px #bdbdbd solid;
	text-align:left;/*	overflow: auto;*/}
.box-container h3 {
	margin: 0;
	padding: 0;
	padding-left: 28px;
	font-family: verdana, arial, sans-serif;
	font-size: 12px;
	line-height: 26px;
	color: #373737;
	background: url(/getImage.php?src=iw-frmSection-ico.gif) transparent no-repeat 6px center;
	cursor: pointer;}
.box-subcontainer {
	height: 26px;
	margin: 0;
	padding: 0;
	font-weight: bold;
	display: block;
	background:  url(/getImage.php?src=iw-frmSection-bgWhite.gif) #f9f9f9 repeat-x left top;
	text-align:left;
	cursor: pointer;}
.box-subcontainer h3 {
	margin: 0;
	padding: 0;
	padding-left: 28px;
	font-family: verdana, arial, sans-serif;
	font-size: 12px;
	line-height: 26px;
	color: #373737;
	background: url(/getImage.php?src=iw-blu-ico.gif) transparent no-repeat 6px center;
	cursor: pointer;}
/* BoX TYPE blue */

.box-container-blue {
	height: 30px;
	margin: 0;
	padding: 0;
	font:normal 16px Arial, Helvetica, sans-serif;
	display: block;
	text-align:left;}
.box-container-blue h3, .box-container-blue h2 {
	margin: 0;
	padding: 0;
	padding-left: 28px;
	font-family: verdana, arial, sans-serif;
	font-size: 12px;
	line-height: 26px;
	color: #FFFFFF;
	background: url(/getImage.php?src=iw-frmSection-ico.gif) transparent no-repeat 6px center;}
/*
Buttons
--------------------------
*/

.button {margin:1px; padding:0px 0 5px 5px; background: url(/getImage.php?src=button.gif) no-repeat left center;}
.button a {
	margin:0px;
	padding:6px 0 5px 5px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 14px;
	font-weight: bold;
	color: #fff;
	text-decoration: none;
	display:block;}
.button a:hover {color: #dd0000; text-decoration: none;}

/*
Form Buttons
---------
*/

.frmButton-over {
	margin:0px;
	padding:0px 5px;
	font-family:tahoma, verdana, sans-serif;
	FONT-SIZE:11px;
	font-weight:bold;
	background:url(/getImage.php?src=form-button.gif) #666666 repeat-x;
	color:#fff97b;
	border:0px;
	height:31px;}
.frmButton-link {font-family:tahoma, verdana, sans-serif; font-size:11px; font-weight:bold;}
.frmButton-link a {
	margin:0px;
	margin-top:2px;
	padding:10px 12px;
	color:#FFFFFF;
	text-decoration:none;
	background:url(/getImage.php?src=form-button.gif) repeat-x;
	line-height:31px;}
.frmButton-link a:hover {color:#c1eb18;}
.frmButton-secondary {
	margin:0px;
	padding:0px 5px;
	font-family:tahoma, verdana, sans-serif;
	FONT-SIZE:11px;
	font-weight:normal;
	background:url(/getImage.php?src=form-button.gif) #666666 repeat-x;
	color:#FFFFFF;
	border:0px;
	height:31px;}
.frmButton-secondary-over {
	margin:0px;
	padding:0px 5px;
	font-family:tahoma, verdana, sans-serif;
	FONT-SIZE:11px;
	font-weight:normal;
	background:url(/getImage.php?src=form-button.gif) #666666 repeat-x;
	color:#fff97b;
	border:0px;
	height:31px;}
/*
Notes
--------------------------
*/


.YellowNote {
	font-family: verdana, arial, sans-serif;
	font-size: 12px;
	font-weight: bold;
	background:  url(/getImage.php?src=ico-casbox.gif) #FFFFD3 no-repeat 5px;
	border-bottom: 1px #C8C86B solid;
	padding: 6px 0 4px 28px;
	margin: 10px 0;
	color: #FA6809;
	text-align: left;
	line-height: 1.2em !important;}
.note {
	font-family: verdana, arial, sans-serif;
	font-size: 11px;
	color: #252525;
	border-bottom: 1px #5DA3E5 solid;
	padding: 3px 10px 2px 22px;
	margin: 0px 0 8px 0;
	background: url(/getImage.php?src=ico-info.gif) #F1F8FF no-repeat 3px 2px;
	text-align:left;}
.GeneralNote {
	font-family: verdana, arial, sans-serif;
	font-size: 11px;
	color: #252525;
	border-bottom: 1px #5DA3E5 solid;
	padding: 3px 10px 2px 22px;
	margin: 8px 0 8px 0;
	background: #F1F8FF;
	text-align:left;}
.secure {background: url(/getImage.php?src=ico-lock.gif) #F1F8FF no-repeat 7px 4px;}
.info {background: url(/getImage.php?src=ico-info.gif) #f9fbfd no-repeat 3px 2px;}
.info p {font-size:11px; padding-left:0px;}
.info ul {margin:0px; padding:0px; padding-left:26px;}
.info li {margin:0px; padding:1px 0; list-style-type:disc;}
.error {
	font:bold 13px Arial, Helvetica, sans-serif;
	background: #ffdbdb url(/getImage.php?src=ico-warning.gif) no-repeat 5px 10px;
	border: 2px solid #ffbaba;
	padding: 10px 0 8px 28px;
	margin: 10px 0;
	color: #6b6868;
	text-align:left;}
.success {
	background: none repeat scroll 0 0 #EAFFEA;
	border: 1px solid #88AA88;
	color: #333333;
	font-size: 12px;
	font-weight: bold;
	margin: 10px 0;
	padding: 10px 0 8px 10px;
	text-align: left;}
.error ul {margin:0px; padding: 5px;}
.error li {
	list-style: none;
	font: 12px Arial, sans-serif;
	color: #5b5b5b;
	text-indent:0;
	line-height: 14px;
	display: block;
	background:  url(/getImage.php?src=bul-error.gif) no-repeat left 4px;
	margin:0px;
	padding:2px 0 5px 15px;
}
.error p {font: 11px Verdana, Arial, sans-serif;}
.global-message {
	-moz-border-radius: 10px 10px 10px 10px;
	background: none repeat scroll 0 0 #FFFBE4;
	border-bottom: 1px solid #C3C4BF;
	margin: 20px 0 15px;
	padding: 10px 0 5px;
	position: relative;
	text-align: center;}
.global-message p {text-align:center; margin:0; padding-bottom:5px}
.global-message .shoping-cart {
	background: url("/getImage.php?src=cart.gif") no-repeat scroll 0 1px transparent;
	font-size: 15px;
	margin: 0 0 0 160px;
	padding-left: 22px;
	text-align:left;}
.global-message .hideLink {position: absolute; right: 20px; top: 13px;}
/*
Tooltip: HoverHelp
--------------------------
*/

blockquote.helpContents {
	margin: 0;
	padding: 5px;
	position: absolute;
	top: -1000px;
	left: -1000px;
	border: 1px solid #000;
	z-index: 100;
	background-color: #F4F4D0;
	-moz-border-radius: 5px;
	-moz-opacity: .75;}
.hasHelp {background: url(/getImage.php?src=ico-help.gif) no-repeat 2px center; padding: 5px 0 2px 25px; border-bottom: 1px #D5D5D5 dashed;}
/*
Context Menu
--------------------------
*/

.context {
	margin:0;
	padding: 8px 12px 0 8px;
	margin-left: 11px;
	margin-bottom: 9px;
	width: 175px;
	text-align: left;}
.context .list_header {
	background:  url(/getImage.php?src=context-header.gif) #A3CCF4 repeat-y top right;
	font-family: verdana, arial, sans-serif;
	font-size: 11px;
	font-weight: bold;
	color: #00305E;
	padding: 2px;
	padding-left:5px;
	border: 1px solid #D6D6D6;
	display: block;}
.context .list {
	margin: 0;
	padding: 3px 0;
	border: 1px solid #D6D6D6;
	border-top: 0 none;
	background: transparent;}
.context .list li {
	list-style: none;
	font: 10px Verdana, Arial, sans-serif;
	color: #c1c1c1;
	text-indent: 4px;
	line-height: 14px;
	display: block;
	background:  url(/getImage.php?src=greenbullet.gif) no-repeat left 3px;
	padding-left: 10px;
	margin-left: 4px;}
.context .list a {text-decoration: none; color: #0560A6;}
.context .list a:hover {color: #FF0000;}
/*
Green Bullets
-----------------------
*/

.greenbullet {color: #252525; margin: 2px; padding-left: 3px;}
.greenbullet ul {margin:0; padding:0;}
.greenbullet li {
	list-style: none;
	background: url(/getImage.php?src=greenbullet.gif) no-repeat left 3px;
	font-family: verdana, arial, sans-serif;
	font-size: 11px;
	color: #252525;
	display: block;
	text-indent: 8px;
	margin-bottom: 3px;
	text-align: left;}
.greenbullet li a {color: #373737; margin-left: 3px;}
.greenbullet li a:hover {color: #ff0000;}
/*
MICS USE ALLS
-----------------------
*/

table.dataTable2 {margin: 0; vertical-align:top; cursor: default}
table.dataTable2 thead td {
	background-color: #f6f6f6;
	border-color: #ffffff #e8eaee #e8eaee #f6f6f6;
	border-style: solid;
	border-width: 1px;
	font-weight: bold;
	padding: 4px 5px;
	color: #333}
table.dataTable2 td {padding: 7px 0px; font-size: 12px; width: 135px;}

/* Data Table */
table.dataTable {
	margin: 0;
	vertical-align:top;
	cursor: default}
table.dataTable thead td {
	background-color: #eeeeee;
	border-color: #F5F7FA #D1D8E5 #D1D8E5 #F5F7FA;
	border-style: solid;
	border-width: 0px;
	font-weight: bold;
	padding: 4px 5px;
	color: #333}
table.dataTable td {
	border-bottom: 0px solid #f5f5f5;
	padding: 5px;
	font:normal 13px Arial, Helvetica, sans-serif;
	}
table.dataTable td .input-width-a {width:210px;}
table.dataTable td .input-width-b {width:157px;}
/* Row Fair */
.row {background-color: #fff}
.row-over {background-color: #E8E8E8}
.row-active {background-color: #EDF7EE}
.PageHeading {
	margin: 0;
	padding: 26px 0px 15px 15px;
	*padding: 16px 0px 15px 15px;
	background: url(/getImage.php?src=header-bg-big-rock.gif) repeat-x;
	font-family: Arial, Helvetica, sans-serif;
	font-size:24px;
	font-weight:bold;
	color: #434343;
	clear: both;
	text-align:left;}
.PageSubHeading {
	margin: 8px 4px;
	padding: 2px 0 2px 9px;
	background: #ECEFDE;
	border-bottom: 1px solid #94A63B;
	font-family: trebuchet ms, verdana, arial, sans-serif;
	font-size: 15px;
	color: #003660;
	clear: both;
	font-weight: bold;
	text-align:left;}
.PageIntro {
	color: #252525;
	font-family: verdana, arial, sans-serif;
	font-size: 11px;
	line-height: 15px;
	text-align: justify;
	padding: 2px 2px 3px 3px;}
.PageIntro ul {margin: 0px; padding: 8px 0px 2px 0px; list-style: none;}
.PageIntro li {margin:0px; padding:1px 0px 3px 20px; background: url(/getImage.php?src=sitebuilder-li.gif) no-repeat 5px 4px;}
.clearfix {clear: both;}
#wrapper {width: 770px;}
.floatLeft {float: left;}
.floatRight {float:right;}
.w760 {width:760px;}
.w145 {width:145px;}
.alt {background-color: #f8f8f8;}
.hilight {color: #0151ba;}
.required {color: #FF0000;}
/*----------  Dashboard ON CSS ----------*/

#dashboard-td {
 margin: 0px;
 padding: 0px;
 font-family: Verdana, Arial, Helvetica, sans-serif;
 font-size: 10px;
 color: #2F2F2F;
 text-align:left;}
ul {margin:0px; padding:1px 0px;}
li {margin:0px; padding:0px; list-style:none; text-decoration:none}
li .red {color: #ff0000;}
.Xheading {border-bottom:1px solid #9DAFC2; background:#CAD8E8;}
.user {
	margin:0px;
	padding:2px 2px 2px 0px;
	font-size:11px;
	color:#024381;
	text-align:left;
	font-weight:bold;}
.botlink {
	margin:0px;
	padding:0px 0px 0px 2px;
	font-size:10px;
	color:#01509A;
	background-color:#eaf3fc;
	border-top: 1px solid #2B4167;}
.botlink a {
	margin:0px;
	padding:0px 0 2px 3px;
	color:#01509A;
	text-decoration:none;}
.botlink a:hover {text-decoration:none; color: red;}
.signout {
	padding:0px;
	padding-right:3px;
	margin:0px 0px;
	text-align:right;}
.signout a {
	padding:0px;
	margin:0px;
	font-size:10px;
	color:#cc0000;
	font-weight:bold;
	text-decoration:underline;}
.signout a:hover {text-decoration:none;}
/*----------  Dashboard OFF CSS ----------*/
.dashboard-off {
	margin: 0px;
	padding: 0px;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 10px;
	color: #2F2F2F;
	text-align:left;}
.dashboard-off .heading {border-bottom:1px solid #9DAFC2; background:#EEEEEE;}
.dashboard-off .user {
	margin:0px;
	padding:2px 2px 2px 0px;
	font-size:11px;
	color:#024381;
	text-align:left;
	font-weight:bold;}
.dashboard-off a {color:#454545; font-weight:bold; font-size: 10px; margin: 0; text-decoration: none;}
.dashboard-off a:hover {text-decoration: underline;}
.dashboard-off .signout {
	padding:0px;
	padding-right:2px;
	margin:0px;
	text-align:right;}
.dashboard-off .signout a {
	padding:0px;
	margin:0px;
	font-size:10px;
	color:#cc0000;
	font-weight:bold;
	text-decoration:none;}
.dashboard-off .signout a:hover {text-decoration:underline;}
/*----------*  Dashboard CSS End *----------*/

.processing_note {font-family: verdana, arial, sans-serif; font-size: 12px; color: #515151;}
/*------------------------------------------backward compatibility -----------------------------*/
.pageintro {
	color: #252525;
	font-family: verdana, arial, sans-serif;
	font-size: 11px;
	line-height: 15px;
	text-align: justify;
	padding: 5px 15px 3px 8px;}
html>img {
	width: 0!important;
	height: 48px!important;
	padding-left: 48px!important;
	background: url(/getImage.php?src=lb-scroll.png) no-repeat;}
.sub-plan {text-indent: 25px;}
.dashboard-checkout {
	background: url(/getImage.php?src=dashboard-checkout.gif) no-repeat left top;
	line-height: 12px;
	height: 15px;
	text-align: left;
	padding-left: 20px;}
.dashboard-checkout a {
	font-size: 10px;
	color: #333;
	text-decoration: none;
	font-family: verdana, arial, sans-serif;
	display: block;}
.dashboard-checkout a:hover {color: red;}
.q-blurb {border-left: 1px solid #565656; border-right: 1px solid #565656;}
.q-blurb p {
	margin: 0 15px;
	padding: 0;
	font-size: 12px;
	font-weight: bold;}
.q-blurb a {
	text-decoration: underline;
	display: block;
	background: url(/getImage.php?src=gray-pointer.gif) no-repeat 1px center;
	margin: 7px 25px 0 25px;
	padding-left: 10px;}
.q-blurb a:hover {text-decoration: none;}
/*
COMMON STYLES: Tuesday, March 28, 2006
*/

.justify-text {text-align: justify;}
#cart_summary {
	overflow:hidden;
	border:1px solid #ccc;
	width:700px;
	position:absolute;
	top:90px;
	right:10%;
	z-index:5000;}
.cart_summary-wrapper {margin:5px; background:#fff;}
/*
HOMEPAGE PRICING TABLE
*/

.hp-pricing-control {
	cursor:pointer;
	background:#f5f5f5;
	border:1px solid #415E8A;
	border-left: 0 none;
	border-right: 0 none;}
.hp-pricing-control strong {margin-left: 3px;}
#hp_pricing_table table .tld-list {background:url(/getImage.php?src=arrow-sidebar-list.gif) no-repeat 2px center; padding-left: 13px;}
#hp_pricing_table table .lowercase {font-size: 10px;}
/*
SELECT LANGUAGE DROPDOWN
*/

#select-language-wrapper {margin-bottom: -5px;}
#select-language-wrapper .small-font {font-size:10px;}
#select-language-table {margin-bottom: 5px;}
.select-language-text strong {font-size:10px; color: #414141;}
.select-language-dropdown select {
	border:1px solid #333;
	color:#555;
	font-family:tahoma;
	font-size:11px;
	margin: 5px 0;
	width: 170px;}
#subcontent2 {
	width: 200px;
	padding: 15px;
	background: #f5f5f5;
	border: 1px solid #c1c1c1;
	z-index: 999;}
/*
REFACTORED CSS
*/

#header-table {background: #ffffff;}
#logo-area .login-text {font-size:12px; color:#434343;}
#logo-area input {border: 2px solid #D3D3D3; font-size:12px; padding:3px 5px;}
#logo-area .login-table td {padding:5px 0;}
#login-div {overflow:hidden;}
#login-box .login-header {background: url(/getImage.php?src=rounded-box-topcap.gif) no-repeat left top;}
#login-box .login-footer {background: url(/getImage.php?src=rounded-box-bottomcap.gif) no-repeat left top;}
#login-box .login-control {border-left: 0px solid #666; border-right: 0px solid #666;}
#login-box .login-area {border-left: 0px solid #666; border-right: 0px solid #666; padding-left:14px;}
.link-underline {margin: 0; padding: 0;}
.link-underline a {
	text-decoration: underline;
	display: block;
	margin: 7px 25px 0 25px;
	padding-left: 10px;}
.link-underline a:hover {text-decoration: none;}
/*
Features Page - supersite-features.css
*/

.box-features-content p {margin-bottom: 7px; padding-bottom: 0;}
.box-features-content h2 {
	background: url(/getImage.php?src=feature-h3-bg.gif) repeat-x left bottom;
	color:#3085c8;
	font-size: 20px;
	font-weight:bold;
	margin: 0;
	padding:7px 0 6px 8px;}
.box-features-content h3 {
	color:#434343;
	font-size: 18px;
	font-weight:bold;
	margin: 0;
	padding:5px 0 0 15px;}
.box-features-content h4 {
	color:#434343;
	font-size: 14px;
	font-weight:bold;
	margin: 0;
	padding:15px 0 0 8px;}
.box-features-content ul {
	margin: 0;
	padding: 0;
	margin-left: 23px;
	margin-bottom: 14px;}
.box-features-content li {
	margin-left: 5px;
	list-style: none;
	background: url(/getImage.php?src=feature-list-ico.gif) no-repeat left 4px;
	margin-top: 10px;
	padding-left: 14px;
	font-size: 12px;}
.box-features-content blockquote {margin: 0; padding: 0; margin-top: 7px;}
.box-features-content blockquote ul {margin: 0;padding: 0; margin-top: 5px;}
.box-features-content blockquote li {
	margin: 0;
	padding: 0;
	list-style: none;
	background: url(/getImage.php?src=dot.gif) no-repeat left 4px;
	padding-left: 10px;
	margin-top: 4px;}
.feature-listheading {
	margin: 0px;
	padding: 0px;
	padding-left: 32px;
	background: url(/getImage.php?src=feature-list-head-ico.gif) no-repeat 17px 4px;
	font-size: 12px;
	display: block;}
.feature-link-ul ul {margin: 0px; padding: 0px;}
.feature-link-ul li {
	margin: 0px;
	padding: 2px 0px;
	padding-left: 16px;
	color: #3274b3;
	background: url(/getImage.php?src=feature-link.gif) no-repeat 0px 2px;}
.feature-link-ul li a {color: #3274b3; display: block;}
.feature-link-ul li a:hover {color: #cc0000;}
.feature-link a {
	margin: 0px;
	padding: 0px 0px;
	padding-top: 3px;
	padding-left: 16px;
	color: #3274b3;
	background: url(/getImage.php?src=feature-link.gif) no-repeat 0px 4px;
	display: block;}
.feature-link a:hover {color: #cc0000;}
#feature-block-header {height: 58px; margin:0; padding:0; background: url(/getImage.php?src=feature-block-header-bg.gif) #fff no-repeat left top;}
#feature-block-header h3 {
	font-size: 14px;
	color: #01559C;
	margin: 0;
	padding: 0;
	padding: 24px 0 10px 35px;
	background: url(/getImage.php?src=feature-block-header-ico.gif) no-repeat 10px 22px;
	overflow: hidden;}
.feature-block-list {
	margin: 0;
	padding: 0;
	width: 196px;
	list-style: none;
	border-left: 2px solid #c1c1c1;
	border-right: 2px solid #c1c1c1;
	background: #fff;}
.feature-block-list li {background: none; padding-bottom: 5px; padding-top: 3px;}
.feature-block-list a {
	display: block;
	background: url(/getImage.php?src=feature-block-list-ico.gif) no-repeat 5px 5px;
	text-decoration: none;
	color: #01559C;
	font-size: 11px;
	padding-left: 15px;}
.feature-block-list a:hover {color: #579A01; background: url(/getImage.php?src=feature-block-list-ico.gif) #f5f5f5 no-repeat 5px 5px;}
#feature-block-footer {background: url(/getImage.php?src=feature-block-footer-bg.gif) #fff no-repeat left top; height: 42px; margin: 0; padding: 0;}
.blurb-box {margin:0px; padding:0px; padding-left:20px; text-align: left;}
.blurb-box a {
	font-family: verdana, arial, sans-serif;
	font-size: 11px;
	text-decoration: none;
	color: #3274b3;
	display: block;
	padding: 5px 5px 5px 20px;
	background: url(/getImage.php?src=blurbs-bg.gif) no-repeat left 5px;}
.blurb-box a:hover {color: #cc0000; background: url(/getImage.php?src=blurbs-hover.gif) no-repeat left 5px;}
/*
Checkout Box supersite-finalcheckout.css
*/

.box-checkout-container {
	height: 26px;
	margin: 0;
	padding: 0;
	font-weight: bold;
	display: block;
	background:  url(/getImage.php?src=iw-frmSection-bg.gif) #f9f9f9 repeat-x left top;
	text-align:left;
	cursor: pointer;}
.box-checkout-content {
	padding: 8px 8px 5px 20px;
	font-family: verdana, arial, sans-serif;
	font-size: 12px;
	line-height: 15px;
	border: 1px #bdbdbd solid;
	text-align:left;
	margin: 0 auto;}
.box-checkout-container h3 {
	margin: 0;
	padding: 0;
	padding-left: 40px;
	font-family: verdana, arial, sans-serif;
	font-size: 12px;
	line-height: 26px;
	color: #FFFFFF;
	background: url(/getImage.php?src=iw-ico-checkout.gif) transparent no-repeat 6px center;}
#support-announcement #sa-header {
	background: url(/getImage.php?src=getImage-announcement.gif) no-repeat scroll left top;
	cursor:pointer;
	height:29px;
	width:683px;}
	
/* FREE services styling start */
.freeservices-checkavail {
	background:#e0e8f1;
	border:1px solid #7aa1cc;
	margin:10px 0 16px;
	padding:10px 10px 15px 10px;}
.freeservices-checkavail form {margin:0; padding:0;}
.freeservices-checkavail a {color:#2d2d2d; padding-right:12px; text-decoration:underline;}
.freeservices-checkavail label {
	font-size:16px;
	font-weight:bold;
	vertical-align:middle;
	margin-right:5px;}
.freeservices-checkavail .search-blurb {
	display: inline-block;
	vertical-align:middle;
	padding:8px 0 15px;}
.freeservices-checkavail .search-blurb div {display:inline;}
.freeservices-checkavail input, .freeservices-checkavail select {vertical-align:middle;}
.input-text {
	border:1px solid #a5a5a5;
	padding:2px;
	margin-right:1px;
 *margin-right:5px;
	width:156px;}
.input-text2 {padding:6px; font-size:14px; margin-right:5px}
.input-select {border:1px solid #a5a5a5; padding:2px; margin-right:4px;}
.freeservices-content {overflow:hidden; _height:1%;}
.freeservices-checkavail p, .freeservices-content p {margin:0; padding:0 0 5px;}
.freeservices-content p {line-height:22px; padding:0 0 10px;}
.freeservices-content ul {margin:0; padding:12px 0 0;}
.freeservices-content ul li {border-bottom:1px solid #e3e3e3; margin-bottom:10px; list-style:none;}
.freeservices-content ul li:last-child {border-bottom:none 0px;}
.freeservices-content ul li strong, .freeservices-content ul li h2 {
	color:#013e6e;
	font-weight:bold;
	font-size:14px;
	font-family:Verdana;
	background-image:url(/getImage.php?src=freeservices-products.gif);
	background-repeat:no-repeat;
	padding:5px 0 0 40px;
	display:block;
	_height:30px;
	min-height:30px;}
.freeservices-content .freeservices-colright li strong, .freeservices-content .freeservices-colright li h2 {padding-left:43px;}
.free-email-icon {background-position:left top;}
.chat-tools-icon {background-position:left -60px;}
.domain-protection-icon {background-position:left -120px;}
.bulk-tools-icon {background-position:left -180px;}
.domain-forwadrding-icon {background-position:left -240px;}
.mail-forwards-icon {background-position:left -300px;}
.dns-management-icon {background-position:left -360px;}
.privacy-protect-icon {background-position:left -420px;}
.control-panel-icon {background-position:left -480px;}
.dedicated-support-icon {background-position:left -540px;}
.freeservices-colleft {float:left; border-right:1px solid #e3e3e3; width:49.8%;}
.freeservices-colleft li {margin:0 18px 0 7px;}
.freeservices-colright {float:right; width:50%;}
.freeservices-colright li {margin-left:18px;}
/* FREE services styling end*/

.no-margin {margin: 0;}
table.dataTable.proceedPayment {clear: both; padding: 5px 0; border-top: 1px solid #E6E6E6; margin: 20px 0 0;}
table.dataTable.proceedPayment td {	padding: 10px 10px 0;}
table.dataTable.proceedPayment form {padding: 10px 0;}
table.dataTable.proceedPayment td #sub_button {float: right; margin-top: -10px;}
.noteWrapper.amountWrapper {padding: 0 5px 0; margin: 16px 0 0;}
.noteWrapper.taxWrapper {padding: 0 5px 0;}
.noteWrapper.totalWrapper {
	border-top: 1px solid #C8C86B;
	background: #FFFFD3;
	padding: 6px 5px 4px 28px;
	font-size: 14px;}
.noteWrapper.discountWrapper {
	color: #70A750;
	font-weight: normal;
	font-size: 12px;
	padding: 0 5px 0;
	display: none;}
.noteWrapper.discountWrapper .discountPercent {display: block; text-align: right; font-size: 12px;}
.noteWrapper {
	clear: right;
	text-align: right;
	float: right;
	font-size:12px;
	font-weight:bold;
	line-height:1.2em !important;
	margin:12px 0 0;}

.wrapper1 {
	color: #44433F;
	font: bold 14px Arial, Helvetica, 'Open Sans', "Myriad Pro", "Gill Sans", Verdana, sans-serif;
	margin: 0 auto;
	padding: 4px 0 0;
	width: 960px;}
.wrapper1 a {color: #ffffff; text-decoration: none;}
.wrapper1 a:hover {color: #494949;}
.wrapper1 p {margin: 0 0 17px; 	padding: 0; line-height: 18px;}
.wrapper {margin: 0px auto;}
.nev-wrapper {clear: both; float: left;}
.content {
	width: 670px;
	float: left;
	padding: 10px 20px;}
.content h1 {
	color: #333;
	font-weight: 400;
	text-transform: uppercase;
	font-size: 18px;
	border-bottom: 1px dashed #C1D9F0;}
.content h2 {
	font-weight: 400;
	text-transform: uppercase;
	font-size: 14px;
	padding-left: 10px;
	margin-bottom: -5px;}
.content p {padding: 0 15px; text-align: justify;}
.content-bottom {
	width: 710px;
	background: transparent url(/getImage.php?src=content_bottom.png) no-repeat;
	height: 13px;
	float: left;}
#active-hostingsection #menu-hostingsection {font-size:14px; color: #494949;
	background: url(/getImage.php?src=menu_mid.gif) repeat 0 42px;}
#active-domainsection #menu-domainsection {font-size:14px; color: #494949; background: url(/getImage.php?src=menu_mid.gif) repeat 0 42px;}
#active-homesection #menu-homesection {font-size:14px; color: #494949; background: url(/getImage.php?src=menu_mid.gif) repeat 0 42px;}
#active-emailsection #menu-emailsection {font-size:14px; color: #494949; background: url(/getImage.php?src=menu_mid.gif) repeat 0 42px;}
#active-sitebuildersection #menu-sitebuildersection {font-size:14px; color: #494949; background: url(/getImage.php?src=menu_mid.gif) repeat 0 42px;}
#active-digicertsection #menu-digicertsection {font-size:14px; color: #494949; background: url(/getImage.php?src=menu_mid.gif) repeat 0 42px;}
.fbConnect {padding: 15px 0 10px;}
.fbConnect span {
	display: inline-block;
	vertical-align: top;
	line-height: 15px;
	margin-right: 5px;}
.fbConnect #fbButton {
	background: url(/getImage.php?src=bg_fb_connect.gif) no-repeat 0 0;
	width: 105px;
	height: 30px;
	cursor: pointer;}
#coupon {background:none repeat scroll 0 0 #FDF8CB; border:1px solid #E6E2C7; height:35px;}
#coupon div {
	background: url("/getImage.php?src=fb_icon.gif") no-repeat scroll 0 4px transparent;
	color: #636363;
	font-size: 16px;
	margin: auto;
	padding: 8px 0 0 35px;
	text-align: left;
	width: 830px;}
.modal-box .modal-box-h1 {font-size: 18px; font-weight: bold; margin:0;}
.modal-box #msg_display {text-align: left; padding: 10px}
.modal-box #msg_display .t1 {font-weight: bold; font-size: 14px; line-height: 24px;}
.modal-box #msg_display .t2 {font-size: 13px; line-height: 20px;}
.modal-box p.ui-button-3-wrapper {text-align: center; padding-left:140px; margin-bottom:40px;}
/*
Footer
--------------------------------------------
*/

.footer {
	margin:0px;
	padding:0px;
	padding-top:4px;
	padding-bottom:4px;
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size:9px;
	color:#FFFFFF;
	text-align:center;
	background:url(/getImage.php?src=footer-bg.gif) repeat-x left top;}
.footer a {color: #fff;}
.footer a:hover {color: #fff; text-decoration: underline;}
body {width: 100%;}
#footer .wide-left-column {width:550px;}
#footer .narrow-right-column {width:380px;}
#footer .section-1 {position: relative; height:240px;}
#footer .section-2 {position: relative;}
#footer .section-2 h3, #footer .section-2 h2 {background:none; font-size:11px; padding-left:0; color:#434343;}
#footer .section-2 h3 a, #footer .section-2 h2 a {color:#434343; text-decoration:none;}
#footer .section-1 .wide-left-column .upper-block {position:relative; height: 81px;}
#footer .section-1 .wide-left-column .lower-block {	position:relative;}
#footer .section-1 .wide-left-column .phone, #footer .section-1 .wide-left-column .help {width: 260px; position:absolute; top: 0; left: 0;}
#footer .section-1 .wide-left-column .media, #footer .section-1 .wide-left-column .products {width: 260px; margin-left: 30px; position:absolute; top: 0; right: 0;}
#footer .section-1 .wide-left-column .upper-block .phone p.toll-free {
	background: url("/getImage.php?src=footer-sprite.jpg") no-repeat scroll 0 0 #D6D6D6;
	height: 35px;
	margin: 0;
	overflow: hidden;
	padding-top: 0;
	text-indent: -999px;
	width: 265px;}

.US #footer .section-1 .wide-left-column .upper-block .phone p.toll-free {background: url("/getImage.php?src=footer-sprite.jpg") no-repeat scroll 0 -260px #D6D6D6; height:70px;}
.US #footer .section-1 .wide-left-column .upper-block .phone p.regular-number {display:none;}
#footer .section-1 .wide-left-column .upper-block .phone p.regular-number {
	background:url("/getImage.php?src=footer-sprite.jpg") no-repeat scroll 0 -30px #D6D6D6;
	height:25px;
	overflow:hidden;
	padding-top:0;
	text-indent:-999px;
	width:250px;
	margin:0;}
#footer .section-1 .wide-left-column .upper-block .media {background:url("/getImage.php?src=footer-sprite.jpg") no-repeat scroll 0px -63px #D6D6D6; height: 55px; overflow: hidden;}
#footer .section-1 .wide-left-column .upper-block .media h2 {display:none;}
#footer .section-1 .wide-left-column .upper-block .media p {
	color:#848484;
	font-size:12px;
	height:25px;
	margin:2px 0 0 60px;
	padding-left:10px;
	padding-top:0;
	width:190px;
	font-weight:normal;}
#footer a {color:#848484; text-decoration: underline;}
#footer .section-1 .wide-left-column .upper-block .media .border {margin:26px 0 0 70px;}
#footer .section-1 .wide-left-column .help .t-need-help {
	background:url("/getImage.php?src=footer-sprite.jpg") no-repeat scroll -275px 3px #D6D6D6;
	color:#848484;
	height:31px;
	overflow:hidden;
	text-indent:-999px;
	width:250px;}
#footer .section-1 .wide-left-column .products .t-our-products {
	background:url("/getImage.php?src=footer-sprite.jpg") no-repeat scroll -274px -31px #D6D6D6;
	height:31px;
	overflow:hidden;
	padding-top:0;
	text-indent:-999px;
	width:250px;
	color: #848484;}
#footer .narrow-right-column .payment .t-payment-method {
	background:url("/getImage.php?src=footer-sprite.jpg") no-repeat scroll 0 -110px #D6D6D6;
	color:#848484;
	height:31px;
	overflow:hidden;
	padding-top:0;
	text-indent:-999px;
	width:348px;
	padding-bottom:10px;}
#footer .narrow-right-column .icann .border {margin-left:60px;}
.section-2 h4 {font-size: 11px; font-weight: bold;}
#footer .narrow-right-column .icann {background:url("/getImage.php?src=footer-sprite.jpg") no-repeat scroll 0 -195px #D6D6D6;}
#footer .narrow-right-column .icann p {margin: 0 0 0 60px; background:none repeat scroll 0 0 #D6D6D6; padding-top:2px;}
#footer .narrow-right-column .payment .cards {background:url("/getImage.php?src=footer-sprite.jpg") no-repeat scroll 0 -155px #D6D6D6; height:32px; margin:10px 0;}
.border {background:url("/getImage.php?src=footer-border.gif") repeat-x scroll 0 bottom transparent; padding-bottom:2px;}
.section-1 {background:url("/getImage.php?src=footer-border.gif") repeat-x scroll 0 bottom transparent; padding-bottom:30px;}
#footer .section-1 .narrow-right-column {background:url("/getImage.php?src=footer-gradient.jpg") no-repeat scroll left -724px transparent; padding-left:30px; width:350px; padding-top:0;}
/*Plan Blurb*/
.plan-blurb {background:url("/getImage.php?src=bg-blurb-plan.jpg") no-repeat left top; width:417px;}
.plan-blurb-header {height:70px; text-align:center; padding:20px 20px 0 10px;}
.plan-blurb-header h3 {
	font-size:23px;
	color:#4a7205;
	font-weight:bold;
	padding:0 0 5px;
	text-shadow:1px 1px 1px #ffffff;}
.plan-blurb-header h4 {
	margin:0;
	font-size:16px;
	color:#171717;
	font-weight:normal;
	text-shadow:1px 1px 1px #ffffff;}
.plan-blurb-title {
	color: #ffffff;
	font-size: 14px;
	height: 60px;
	line-height: 20px;
	margin: 6px 20px 0;
	text-align: center;}
.plan-blurb-content {padding:0 25px 30px; height:218px; background:url("/getImage.php?src=bg-blurb-plan.jpg") no-repeat bottom center;}
.plan-blurb-content .ul-sign-bullet {padding:0; margin:0;}
.plan-blurb-footer {text-align:center; margin-top:20px;}
.plan-blurb-footer .ui-button {margin-bottom:15px;}
.plan-blurb-footer .footer-note {font-style:italic; font-size:12px;	color:#4a4a4a;}
/*Main Banner Area*/
.main-banner {margin:20px 0 0}
.reseller-partner-banner {
	background:url(/getImage.php?src=banner-partner-plan.jpg) no-repeat left top;
	width:875px;
	height:308px;
	position:relative;}
.reseller-partner-banner .offer-price {
	position:absolute;
	right:125px;
	top:38px;
	color:#fa7109;
	font-size:32px;
	font-weight:bold;
	text-shadow:1px 1px 1px #000}
.reseller-partner-banner .banner-content {
	padding:190px 0 0 240px;}
.reseller-partner-banner h3 {
	padding:0 0 10px;
	font-weight:normal;
	font-size:20px;
	color:#1c1c1c}
.reseller-partner-banner h4 {
	margin:0;
	font-size:18px;
	font-weight:normal;
	color:#313131;}
/* Cart Bar */
#cart_link {
	background: none repeat scroll 0 0 #FFFBE4;
	border-bottom: 1px solid #C3C4BF;
	height: 40px;
	line-height: 45px;
	text-align: center;
	margin:20px 0 15px;
	padding:0;
	position:relative;
	-moz-border-radius: 10px;
	border-radius: 10px;}
#cart_link p {
	background: url("/getImage.php?src=cart.gif") no-repeat scroll 0 12px transparent;
	font-size: 15px;
	line-height: 40px;
	margin: 0 0 0 160px;
	padding-left: 22px;}
#cartbar-hideLink {position:absolute; top:0; right:20px;}
#checkout_arrow {
	background: url("/getImage.php?src=bg_checkout_arrow_8.png") repeat scroll 0 0 transparent;
	display: block;
	height: 65px;
	overflow: hidden;
	position: absolute;
	right: 13px;
	top: 103px;
	width: 103px;
	z-index: 999;}
html>body #checkout_arrow {	background: url("/getImage.php?src=bg_checkout_arrow_24.png") repeat scroll 0 0 transparent;}
	
/* Section Bottom Blurb */
#section-bottom {margin:0 30px 50px;}
#section-bottom {background:none repeat scroll 0 0 #FFFFFF;}
#section-bottom h1, #section-bottom h3 {
	color: #656565;
	font-size: 28px;
	font-weight: bold;
	margin: auto;
	padding-top: 10px;
	text-align: center;}
#section-bottom h1#bulkRegister, #section-bottom h3#bulkRegister {font-size: 22px;}
#section-bottom #help {
	background:url("/getImage.php?src=sb-plans-sprite.jpg") no-repeat scroll 0 -125px transparent;
	padding-bottom:1px;
	padding-left:40px;
	padding-top:1px;
	text-align:left;
	height:94px;
	margin-top:20px;}
#section-bottom #help .left, #section-bottom #help .center, #section-bottom #help .right {float:left;}
#section-bottom .rbtop {background:#f8f8f8;}
#section-bottom .rbbot {background: transparent; margin-top:-7px;}
#section-bottom .rbtop div {background:url("/getImage.php?src=sbplans-top-right-curve.gif") no-repeat scroll right top transparent;}
#section-bottom .rbbot div {background:url("/getImage.php?src=sbplans-bottom-right-curve.gif") no-repeat scroll right top transparent;}
#section-bottom .rbtop div span {background: none; margin:0 6px 0 0;}
#section-bottom .rbbot div span {background: none; margin:0 6px 0 0;}
#section-bottom .rbcontent {border-left: none; background: none; background:url("/getImage.php?src=sbplans-help-bg.jpg") repeat-x scroll 0 bottom #F8F8F8; padding:0; height:88px;}
#section-bottom #help .left {width:270px; border-right: 1px solid #dedcdc;}
#section-bottom #help .left .toll-free {background:url("/getImage.php?src=sb-plans-sprite.jpg") no-repeat scroll -38px -134px transparent; margin-bottom:0; margin-top:5px; padding:5px 86px 0 22px;}
#section-bottom #help .left .toll-free .orange-text {font-size:22px; font-weight:bold;}
#section-bottom #help .left .toll-free .number {color: #646464; font-size: 14px;}
#section-bottom #help .left .toll-free .email {margin-top:10px;}
#section-bottom .live-chat-icon {background:url("/getImage.php?src=sb-plans-sprite.jpg") no-repeat scroll -7px -228px transparent; height:40px; width:40px; float: left;}
#section-bottom .query-icon {background:url("/getImage.php?src=sb-plans-sprite.jpg") no-repeat scroll -7px -277px transparent; height:55px; width:50px; float: left;}
#section-bottom .center {padding:10px 15px 0; border-right:1px solid #DEDCDC;}
#section-bottom .right {padding:10px 15px;}
#section-bottom .query {float: left; text-align: left;}
#section-bottom .live-chat {float: left; text-align: left;}
#help h4 {text-align: left; margin:0 0 10px 5px;}
.rbcontent .ui-button {width:119px; color: #FFFFFF;}
/*Pricng table*/
.pricing-table {table-layout:fixed;}
.pricing-table thead {
	background:#FFF;
	font-size:19px;
	font-weight:bold;
	color:#393939;}
.pricing-table thead td {
	padding:8px 0;
	font-size: 15px;
	border:1px solid #FFF;
	border-bottom: 1px solid #DDDDDD;
	background:#FFF;}
.pricing-table thead td small {
	color: #4C4C4C;
	display: block;
	font-size: 12px;
	font-weight: normal;}
.pricing-table thead .tld-col {color: #393939; font-size: 18px;}
.pricing-table tbody td.tld-col {
	font-size: 24px;
	font-weight: bold;
	position: relative;
	text-align: center;
	width: 200px;}
.pricing-table tbody td {
	font-size:14px;
	color:#575757;
	height: 50px;
	padding: 4px 0 10px;
	border-color: #FFFFFF #FFFFFF #ddd;
	border-style: solid;
	border-width: 1px;}
.pricing-table tbody td.tld-other-price {
	font-size:14px;
	color:#4c4c4c;
	padding: 4px 0 10px;
	border-color: #FFFFFF #FFFFFF #ddd;
	border-style: solid;
	border-width: 1px;}
.pricing-table tbody tr {background:#F6F6F6;}
.pricing-table tbody tr.alternate, .pricing-table tbody tr.alternatepromo {background:#fff;}
.pricing-table th {
	background: none repeat scroll 0 0 #EDEDED;
	border-bottom: 2px solid #FFFFFF;
	color: #0F0F0F;
	font: bold 17px Arial, Helvetica, sans-serif;
	text-align:center;
	height:40px;}
.pricing-table tbody td.tld-col {
	font:normal 17px Arial, Helvetica, sans-serif;
	color:#575757;
	text-align: center;
	width: 200px;
	position:relative;}
.pricing-table tbody td.tld-col small {display: block; font-size: 12px; font-weight: normal; color:#777;}
.pricing-table tbody td.tld-col-other-ext {font-size: 18px; font-weight: bold;}
.pricing-table td {text-align:center;}
.pricing-table tbody .low-price {color: #377CE4;}
.icon-money-back {
	background: url("/getImage.php?src=side-bar-info-icons.gif") no-repeat scroll 0 0 transparent;
	float: left;
	height: 86px;
	margin-top: -13px;
	width: 76px;}
	
/*Bg Blurb*/
.bg-blurb {background: url("/getImage.php?src=seo-sprite.gif") repeat-x left top; padding:0 20px 20px;}
.bg-blurb h2 {border-bottom:1px solid #dadada; text-align:center; margin:0 0 20px}
.bg-blurb-footer .bg-blurb-title {
	font-size:24px;
	font-weight:bold;
	text-shadow:1px 1px 1px #000;
	padding:0 0 3px;}
.bg-blurb-footer span {color:#f27322;}
.bg-blurb-footer {
	text-align:center;
	padding:7px 0 10px 0;
	margin-bottom:20px;
	font-size:14px;
	color:#ffffff;
	border-top:1px solid #292928;
	border-right:1px solid #0d0d0d;
	border-bottom:1px solid #010101;
	border-left:1px solid #080808;
	background:#383b3c url("/getImage.php?src=seo-sprite.gif") repeat-x left -376px;
	-moz-border-radius:0 0 8px 8px;
	-webkit-border-radius:0 0 8px 8px;
	border-radius:0 0 8px 8px}
.wide-left-column .bg-blurb-footer {margin:0 20px 20px 0;}
.bdr-blurb {border:1px solid #f2f2f2; padding:15px;}
.wide-left-column .bdr-blurb {margin:0 20px 0 0;}
.bdr-blurb h2, .bdr-blurb h3, .bdr-blurb .ul-sign-bullet {padding-left:0;}
.bdr-blurb h2 {font-size:25px; padding-bottom:20px;}
#page-header {color:#5b5b5b; /*padding:15px 0 0;*/}
#page-header h1 {font-size:36px; height:90px; width:450px; overflow:hidden;}
#page-header h1 .logo-cont{ display:table-cell; height:90px; vertical-align:middle;}

#page-header #header-help-note {font-size:24px; font-weight:normal}
#header-label {font-size:15px; color:#7c7c7c; font-weight:normal;}
#header-label span {color:#fa710a;}
.ui-h2 {background:none; color:#5b5b5b;}
#ie6-wrapper {}
#ie6-header {
	background:#fffdf0 url(/getImage.php?src=bg-ie6-header-message.gif) no-repeat left top;
	/*width:965px;*/
	width:1000px;
	height:61px;
	margin:0 auto;
	border-radius: 0 0 8px 8px;
	}
#ie-header-message {float:left; padding:18px 0 0 50px; width:614px;}
#ie-header-message strong {font-size:13px;}
#ie6-browser-wrap {float:left; padding:15px 0 0 0;}
#ie-chorme {width:45px; padding:5px 0 5px 25px; display:inline-block; margin-right:5px}
#ie-firfox { width:40px; padding:5px 0 5px 26px; display:inline-block; margin-right:5px}
#ie-iexplorer {width:90px; padding:5px 0 5px 26px; display:inline-block; margin-right:5px}
	
/*customer speaks*/
.customer-speaks {margin-top:20px;}
.customer-speaks h2, .customer-speaks .customer-speaks-heading {
	background: url("/getImage.php?src=bg-customer-speaks-h2.gif") no-repeat;
	color:#656565;
	font-size:18px;
	padding-bottom:15px;
	padding-left:8px;
	margin-bottom:10px;}
.customer-speaks .customer-speaks-heading {padding-top:6px; font-weight:bold;}
.customer-speaks ul {margin:0; padding:0;}
.customer-speaks ul li {margin-bottom:15px;}
.customer-speaks h5, .customer-speaks p {margin:0; padding:0;}
.customer-speaks h5 {font-weight:bold; font-size:16px; margin-bottom:5px; color:#656565;}
.customer-speaks-wrp {margin:0 0 0 70px;}
.customer-speaks-blurb {
	background:url("/getImage.php?src=bg-customer-speaks-blurb.gif") no-repeat left top;
	color:#808080;
	font-size:13px;
	padding:20px 10px 5px 32px;
	line-height:18px;}
.customer-speaks-blurb h5 {font-size:16px;}
.customer-speaks-img {
	border:1px solid #dbdbdb;
	background:#f5f5f5;
	padding:5px;
	float:left}
.h5-helpnote {color: #808080; font-size: 12px; font-weight: normal;}

/*Plan Detials*/
.plan-col-1 {margin-left: 17px;}
.plan-details p.b-border {border-bottom: 1px solid #F0F0F0; margin: 0 7px 8px;}
.plan-details {display: inline; float: left; margin:0 0 15px 11px; width: 222px;}
.plan-details-blurb {height:135px; padding:95px 0 17px;}
.plan-details p {font-size: 14px; margin: 0 0 8px; padding: 0 0 4px; text-align: center;}
.plan-col-2 p.b-border {border-bottom: 1px solid #F4D377;}
.plan-cost {font-size: 25px; font-weight: bold;}
.price-duration {font-size: 20px; font-weight: normal;}
/*Curly Box*/
.curly-blurb {position:relative; background:url(/getImage.php?src=curly-blurb-sprite.gif) repeat-x left -464px; min-height:363px;}
.curly-left-curve {
	background:url(/getImage.php?src=curly-blurb-sprite.gif) no-repeat left -63px;
	width:5px;
	height:363px;
	display:inline-block;
	position:absolute;
	left:0;
	bottom:0}
.curly-right-curve {
	background:url(/getImage.php?src=curly-blurb-sprite.gif) no-repeat -23px -63px;
	width:5px;
	height:363px;
	display:inline-block;
	position:absolute;
	right:0;
	bottom:0;}
.curly-blurb-wrp {padding:24px 25px 0 30px;}
.curly-blurb p {padding-left:0; line-height:22px; font-size:16px;}
.curly-blurb-content {width:480px}
.curly-blurb h2 {
	background:none;
	font-weight:normal;
	color:#5b5b5b;
	font-size:26px;
	padding:0;}
.step-icon {
	background:url(/getImage.php?src=curly-blurb-sprite.gif) no-repeat left top;
	font-size:20px;
	font-weight:bold;
	color:#ffffff;
	text-align:center;
	width:28px;
	height:29px;
	display:inline-block;
	vertical-align:middle;}
.step-content {border-bottom:1px solid #cccccc;}
.ul-step li {
	color:#5b5b5b;
	font-size:16px;
	clear:both;
	padding:8px 0 0 0;}
.step-content {
	border-bottom:1px solid #cccccc;
	margin:0 0 0 40px;
	padding:2px 0 10px 0;}
.ul-step .last-step .step-content {border-bottom:none;}
.curly-blurb ul#banner-slider {padding-top:5px;}
/* refer a friend common styles*/
.green-button-bg {
	background: url("/getImage.php?src=green-button-bg.gif") no-repeat scroll 0 9px transparent;
	color: #FFFFFF;
	display: block;
	font-family: arial;
	font-size: 12px;
	font-weight: normal;
	text-align: center;
	text-decoration: none;
	width: 84px;
	cursor: pointer;}
#rememberme {margin:0 0 15px 0;}
.seperator {height:10px; margin:15px 0; border-top:solid 1px #e5e5e5;}
.thumb-email {position:absolute; top:37px; right:0;}
.more-link a{font:normal 13px Arial, Helvetica, sans-serif; padding:5px 0 0 0;}
.table-grid tr td{padding:10px 0; border-bottom:solid 1px #eee; font:normal 14px Arial, Helvetica, sans-serif; color:#5b5b5b;}	
.glow-field{
	padding:5px;
	font:bold 14px Arial, Helvetica, sans-serif;
	border:solid 1px #c1c1c1;
	box-shadow:1px 1px 1px #D4D4D4 inset;
	-webkit-box-shadow:1px 1px 1px #D4D4D4 inset;
	-moz-box-shadow:1px 1px 1px #D4D4D4 inset;
	-ms-box-shadow:1px 1px 1px #D4D4D4 inset;				
	color:#5b5b5b;	}	
.table-grid .totalcost-icon	td{border:none; font:bold 16px Arial, Helvetica, sans-serif; padding:20px 0 15px 0;}
#pto{ padding-left:15px;}
.dlist-gray{   color: #7A7A7A; font: 20px Arial,Helvetica,sans-serif; margin: 0; padding: 5px 0 15px;}
.oflow{ min-height:500px;}
.invisible { display: none; }
iframe[src="about:blank"] {
    display:none;
}

.error-message {
    background:#fff8f8;
    border:1px solid #FF0000;
    color:#FF0000;
    font-size:12px;
    font-weight:bold;
    line-height:1.2em !important;
    margin:10px 0;
    padding:10px 0 8px 28px;
    text-align:left;
}

/* dot NGO/ONG */

#dotngotnc{
	margin: 5px 0 0 0;
}

label#tnc-txt{
	font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    display: inline-block;
    width: 650px;
    vertical-align: top;
    color: #1b1b1b;
	margin: 0 0 0 6px;
}